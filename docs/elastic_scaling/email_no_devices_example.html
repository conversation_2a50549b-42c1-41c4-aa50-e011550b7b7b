<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>【弹性伸缩】入池变更通知 - 订单号：弹性扩容-紧急策略-20241201-1700</title>
</head>
<body style="margin: 0; padding: 20px; font-family: 'Microsoft YaHei', Arial, sans-serif; background-color: #f5f7fa;">
    <div style="max-width: 800px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
        <!-- 邮件头部 -->
        <div style="background: linear-gradient(135deg, #ff7a45 0%, #d4380d 100%); color: white; padding: 24px; border-radius: 8px 8px 0 0;">
            <h1 style="margin: 0; font-size: 24px; font-weight: 600;">
                ⚠️ 入池变更通知（设备不足）
            </h1>
            <p style="margin: 8px 0 0 0; font-size: 14px; opacity: 0.9;">
                订单号：弹性扩容-紧急策略-20241201-1700 | 创建时间：2024-12-01 17:00:35
            </p>
        </div>

        <!-- 邮件正文 -->
        <div style="padding: 32px;">
            <!-- 问候语 -->
            <div style="margin-bottom: 24px;">
                <h2 style="color: #333; font-size: 18px; margin: 0 0 12px 0;">👋 值班同事，您好！</h2>
                <p style="color: #666; font-size: 14px; line-height: 1.6; margin: 0;">
                    系统检测到集群资源需要进行<strong style="color: #ff7a45;">入池</strong>变更操作，但<strong style="color: #ff4d4f;">无法找到可用设备</strong>，请协调处理相关工作。
                </p>
            </div>

            <!-- 订单详情 -->
            <div style="margin-bottom: 24px;">
                <h3 style="color: #262626; margin: 0 0 20px 0; font-size: 18px; font-weight: 600; display: flex; align-items: center;">
                    <span style="margin-right: 8px;">📋</span>
                    <span>订单详情</span>
                </h3>
                <div style="background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 8px; padding: 24px; border: 1px solid #e9ecef; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <table style="width: 100%; border-collapse: separate; border-spacing: 0;">
                        <tr>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; color: #8c8c8c; font-size: 13px; font-weight: 500; width: 140px; vertical-align: middle;">
                                <span style="display: inline-block; width: 4px; height: 16px; background-color: #1890ff; margin-right: 8px; border-radius: 2px;"></span>
                                订单号
                            </td>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; font-size: 14px; font-weight: 600; color: #262626; font-family: 'Courier New', monospace;">弹性扩容-紧急策略-20241201-1700</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; color: #8c8c8c; font-size: 13px; font-weight: 500; vertical-align: middle;">
                                <span style="display: inline-block; width: 4px; height: 16px; background-color: #ff7a45; margin-right: 8px; border-radius: 2px;"></span>
                                操作类型
                            </td>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; font-size: 14px; font-weight: 700;">
                                <span style="color: #ff7a45; background-color: #ff7a4520; padding: 4px 12px; border-radius: 16px; font-size: 13px;">入池</span>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; color: #8c8c8c; font-size: 13px; font-weight: 500; vertical-align: middle;">
                                <span style="display: inline-block; width: 4px; height: 16px; background-color: #52c41a; margin-right: 8px; border-radius: 2px;"></span>
                                目标集群
                            </td>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; font-size: 14px; font-weight: 600; color: #262626;">生产集群-C</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; color: #8c8c8c; font-size: 13px; font-weight: 500; vertical-align: middle;">
                                <span style="display: inline-block; width: 4px; height: 16px; background-color: #722ed1; margin-right: 8px; border-radius: 2px;"></span>
                                资源池类型
                            </td>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; font-size: 14px; font-weight: 600; color: #262626;">
                                <span style="background-color: #f6f6f6; color: #595959; padding: 2px 8px; border-radius: 4px; font-size: 12px; font-family: monospace;">worker</span>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; color: #8c8c8c; font-size: 13px; font-weight: 500; vertical-align: middle;">
                                <span style="display: inline-block; width: 4px; height: 16px; background-color: #fa8c16; margin-right: 8px; border-radius: 2px;"></span>
                                需要设备数量
                            </td>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; font-size: 16px; font-weight: 700; color: #262626;">
                                <span style="color: #fa8c16;">0</span> <span style="font-size: 12px; color: #8c8c8c; font-weight: 400;">台</span>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; color: #8c8c8c; font-size: 13px; font-weight: 500; vertical-align: middle;">
                                <span style="display: inline-block; width: 4px; height: 16px; background-color: #9254de; margin-right: 8px; border-radius: 2px;"></span>
                                创建人
                            </td>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; font-size: 14px; font-weight: 600; color: #262626;">
                                <span style="background-color: #f0f5ff; color: #1890ff; padding: 2px 8px; border-radius: 4px; font-size: 12px;">system</span>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 12px 16px; color: #8c8c8c; font-size: 13px; font-weight: 500; vertical-align: top; padding-top: 16px;">
                                <span style="display: inline-block; width: 4px; height: 16px; background-color: #13c2c2; margin-right: 8px; border-radius: 2px;"></span>
                                触发原因
                            </td>
                            <td style="padding: 12px 16px; font-size: 14px; line-height: 1.6; color: #595959; padding-top: 16px;">
                                <div style="background-color: #fafafa; padding: 12px; border-radius: 4px; border-left: 3px solid #13c2c2;">
                                    策略 [高负载监控策略] 为集群 [production-cluster]（compute类型）触发入池操作。<br/>
                                    但未匹配到合适设备，请关注。
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- 设备不足提醒 -->
            <div style="border-left: 4px solid #ff4d4f; background-color: #fff2f0; padding: 20px; margin-bottom: 24px;">
                <h3 style="color: #cf1322; margin: 0 0 12px 0; font-size: 16px;">🚫 设备不足情况</h3>
                <div style="background-color: white; border-radius: 6px; padding: 16px; margin-bottom: 16px; text-align: center;">
                    <div style="font-size: 48px; color: #ff4d4f; margin-bottom: 16px;">⚠️</div>
                    <p style="font-size: 16px; color: #cf1322; font-weight: 600; margin: 0;">
                        未找到可用设备执行入池操作
                    </p>
                </div>
                <p style="color: #a8071a; font-size: 13px; line-height: 1.6; margin: 0;">
                    <strong>注意：</strong> 本次操作未匹配到具体设备，请相关人员关注并手动处理。
                </p>
            </div>

            <!-- 处理指引 -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 6px; padding: 20px; margin-bottom: 24px; color: white;">
                <h3 style="margin: 0 0 12px 0; font-size: 16px;">⚡ 处理指引</h3>
                <ul style="margin: 0; padding-left: 20px; line-height: 1.8;">
                    <li>检查集群中可用设备状态，确认是否有可调配的设备</li>
                    <li>联系设备管理团队，申请新的可用设备</li>
                    <li>评估是否可以从其他集群调配设备资源</li>
                    <li>考虑临时增加虚拟机节点作为过渡方案</li>
                    <li>如无法及时获得设备，评估是否可以忽略此次扩容需求</li>
                    <li>完成设备协调后，请手动创建入池订单或重新触发策略评估</li>
                </ul>
            </div>

            <!-- 重要提醒 -->
            <div style="border-left: 4px solid #ff4d4f; background-color: #fff2f0; padding: 16px; margin-bottom: 24px;">
                <h4 style="color: #cf1322; margin: 0 0 8px 0; font-size: 14px;">⚠️ 重要提醒</h4>
                <p style="color: #a8071a; font-size: 13px; line-height: 1.6; margin: 0;">
                    集群资源分配率已连续超过阈值，建议<strong>尽快协调设备资源</strong>以避免性能问题。
                    如短期内无法获得设备，请考虑其他优化措施或临时扩容方案。
                </p>
            </div>

            <!-- 联系信息 -->
            <div style="text-align: center; padding: 20px; background-color: #f8f9fa; border-radius: 6px;">
                <p style="color: #666; font-size: 13px; margin: 0 0 8px 0;">
                    如有疑问，请联系技术支持团队：<EMAIL> | 紧急热线：400-800-8888
                </p>
                <p style="color: #999; font-size: 12px; margin: 0;">
                    此邮件由弹性伸缩系统自动发送，请勿直接回复
                </p>
            </div>
        </div>

        <!-- 邮件底部 -->
        <div style="background-color: #f1f3f4; padding: 16px 32px; border-radius: 0 0 8px 8px; text-align: center;">
            <p style="color: #666; font-size: 12px; margin: 0;">
                © 2024 弹性伸缩管理系统 | 发送时间：2024-12-01 17:00:35
            </p>
        </div>
    </div>
</body>
</html> 