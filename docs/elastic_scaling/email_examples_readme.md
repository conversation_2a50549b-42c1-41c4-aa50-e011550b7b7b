# 弹性伸缩邮件通知示例

本目录包含弹性伸缩系统邮件通知的三种主要场景示例HTML，展示了基于当前邮件生成逻辑 (`server/portal/internal/service/elastic_scaling_order.go`) 的实际邮件效果。

## 示例文件

### 1. 入池操作邮件示例
**文件**: `email_pool_entry_example.html`

**场景**: 集群资源分配率超过阈值，需要增加设备入池
- **主题色**: 绿色 (`#52c41a` - `#389e0d`)
- **图标**: 🚀 入池变更通知
- **包含设备列表**: 3台可用设备
- **操作指引**: 设备入池流程
- **时间要求**: 2小时内完成

**关键信息展示**：
- 友好的问候语和操作说明
- 订单详情（订单号、操作类型、目标集群等）
- 涉及设备的详细信息表格
- 预计操作后的资源分配率变化
- 详细的操作步骤指引

### 2. 退池操作邮件示例
**文件**: `email_pool_exit_example.html`

**场景**: 集群资源分配率长期低于阈值，需要移除设备退池
- **主题色**: 橙色 (`#ff7a45` - `#d4380d`)
- **图标**: 📉 退池变更通知
- **包含设备列表**: 2台使用中设备
- **操作指引**: 设备退池流程（包含负载迁移）
- **安全提醒**: 确保负载迁移完成

**关键信息展示**：
- 友好的问候语和操作说明
- 订单详情和触发原因
- 需要退池的设备信息
- 预计操作后的资源分配率变化
- 安全操作流程和注意事项

### 3. 设备不足情况邮件示例
**文件**: `email_no_devices_example.html`

**场景**: 需要入池但无可用设备
- **主题色**: 警告橙色 (`#ff7a45` - `#d4380d`)
- **图标**: ⚠️ 入池变更通知（设备不足）
- **无设备列表**: 显示未找到可用设备
- **处理指引**: 设备协调和申请流程
- **重要提醒**: 尽快协调设备资源

**关键信息展示**：
- 友好的问候语和操作说明
- 订单详情和设备需求
- 设备不足的警告提示
- 详细的处理指引和协调建议
- 紧急情况的处理方案

## 技术实现对应关系

### 邮件生成入口
```go
// server/portal/internal/service/elastic_scaling_order.go
func (s *ElasticScalingService) generateOrderNotificationEmail(orderID int64, dto OrderDTO) string
```

### 核心方法映射

1. **订单信息获取**
   - `getActionName()`: 操作类型转换（入池/退池）
   - 集群名称查询
   - 设备信息查询

2. **邮件内容构建**
   - `buildEmailHTML()`: 构建完整HTML结构
   - 根据 `len(devices) > 0` 决定显示设备列表还是无设备提醒
   - 自动生成问候语和主题色彩

3. **样式和主题**
   - 入池: 绿色主题 (`#52c41a` - `#389e0d`) + 🚀 图标
   - 退池: 橙色主题 (`#ff7a45` - `#d4380d`) + 📉 图标
   - 设备不足: 警告橙色主题 + ⚠️ 图标

4. **新增功能**
   - 自动生成问候语文本
   - 根据操作类型显示不同的操作指引
   - 智能判断显示时间要求或安全提醒

### 数据来源

- **订单信息**: `OrderDTO` 结构体
- **设备信息**: `getDeviceInfoForEmail()` 方法查询
- **集群信息**: K8s集群表查询
- **触发原因**: 策略描述和资源快照信息

## 样式特点

### 响应式设计
- 最大宽度800px，自动居中
- 移动端友好的viewport设置
- 表格支持横向滚动

### 视觉层次
- 清晰的头部区域（渐变背景）
- 分块的内容区域（背景色区分）
- 统一的内边距和圆角设计

### 颜色体系
- **绿色系**: 成功、入池操作
- **橙色系**: 警告、退池操作、设备不足
- **灰色系**: 文本、背景、边框
- **红色系**: 错误、紧急提醒

### 图标语义
- 🚀: 入池扩容
- 📉: 退池缩容
- ⚠️: 警告提醒
- 📋: 订单详情
- 🖥️: 设备信息
- ⚡: 操作指引
- ⏰: 时间要求

## 使用建议

### 邮件客户端兼容性
- 使用内联CSS确保跨客户端兼容
- 避免使用外部CSS文件
- 使用table布局确保邮件客户端支持

### 内容个性化
- 根据实际业务需求调整联系方式
- 根据企业环境调整操作流程
- 根据集群命名规范调整示例数据

### 模板扩展
这些示例可以作为模板基础，支持以下扩展：
- 多语言版本
- 自定义颜色主题
- 添加企业Logo
- 集成企业通讯录
- 添加操作追踪链接

## 相关文档

- [邮件通知功能文档](email_notification_feature.md)
- [弹性伸缩总体设计](elastic_scaling_design.md)
- [后端变更总结](BACKEND_CHANGES_SUMMARY.md) 