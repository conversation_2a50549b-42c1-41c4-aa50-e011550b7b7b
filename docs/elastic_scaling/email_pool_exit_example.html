<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>【弹性伸缩】退池变更通知 - 订单号：弹性缩容-低负载策略-20241201-1645</title>
</head>
<body style="margin: 0; padding: 20px; font-family: 'Microsoft YaHei', Arial, sans-serif; background-color: #f5f7fa;">
    <div style="max-width: 800px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
        <!-- 邮件头部 -->
        <div style="background: linear-gradient(135deg, #ff7a45 0%, #d4380d 100%); color: white; padding: 24px; border-radius: 8px 8px 0 0;">
            <h1 style="margin: 0; font-size: 24px; font-weight: 600;">
                📉 退池变更通知
            </h1>
            <p style="margin: 8px 0 0 0; font-size: 14px; opacity: 0.9;">
                订单号：弹性缩容-低负载策略-20241201-1645 | 创建时间：2024-12-01 16:45:12
            </p>
        </div>

        <!-- 邮件正文 -->
        <div style="padding: 32px;">
            <!-- 问候语 -->
            <div style="margin-bottom: 24px;">
                <h2 style="color: #333; font-size: 18px; margin: 0 0 12px 0;">👋 值班同事，您好！</h2>
                <p style="color: #666; font-size: 14px; line-height: 1.6; margin: 0;">
                    系统检测到集群资源需要进行<strong style="color: #ff7a45;">退池</strong>变更操作，已匹配到 2 台设备，请及时处理。
                </p>
            </div>

            <!-- 订单详情 -->
            <div style="margin-bottom: 24px;">
                <h3 style="color: #262626; margin: 0 0 20px 0; font-size: 18px; font-weight: 600; display: flex; align-items: center;">
                    <span style="margin-right: 8px;">📋</span>
                    <span>订单详情</span>
                </h3>
                <div style="background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 8px; padding: 24px; border: 1px solid #e9ecef; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <table style="width: 100%; border-collapse: separate; border-spacing: 0;">
                        <tr>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; color: #8c8c8c; font-size: 13px; font-weight: 500; width: 140px; vertical-align: middle;">
                                <span style="display: inline-block; width: 4px; height: 16px; background-color: #1890ff; margin-right: 8px; border-radius: 2px;"></span>
                                订单号
                            </td>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; font-size: 14px; font-weight: 600; color: #262626; font-family: 'Courier New', monospace;">弹性缩容-低负载策略-20241201-1645</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; color: #8c8c8c; font-size: 13px; font-weight: 500; vertical-align: middle;">
                                <span style="display: inline-block; width: 4px; height: 16px; background-color: #ff7a45; margin-right: 8px; border-radius: 2px;"></span>
                                操作类型
                            </td>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; font-size: 14px; font-weight: 700;">
                                <span style="color: #ff7a45; background-color: #ff7a4520; padding: 4px 12px; border-radius: 16px; font-size: 13px;">退池</span>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; color: #8c8c8c; font-size: 13px; font-weight: 500; vertical-align: middle;">
                                <span style="display: inline-block; width: 4px; height: 16px; background-color: #52c41a; margin-right: 8px; border-radius: 2px;"></span>
                                目标集群
                            </td>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; font-size: 14px; font-weight: 600; color: #262626;">测试集群-B</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; color: #8c8c8c; font-size: 13px; font-weight: 500; vertical-align: middle;">
                                <span style="display: inline-block; width: 4px; height: 16px; background-color: #722ed1; margin-right: 8px; border-radius: 2px;"></span>
                                资源池类型
                            </td>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; font-size: 14px; font-weight: 600; color: #262626;">
                                <span style="background-color: #f6f6f6; color: #595959; padding: 2px 8px; border-radius: 4px; font-size: 12px; font-family: monospace;">worker</span>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; color: #8c8c8c; font-size: 13px; font-weight: 500; vertical-align: middle;">
                                <span style="display: inline-block; width: 4px; height: 16px; background-color: #fa8c16; margin-right: 8px; border-radius: 2px;"></span>
                                需要设备数量
                            </td>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; font-size: 16px; font-weight: 700; color: #262626;">
                                <span style="color: #fa8c16;">2</span> <span style="font-size: 12px; color: #8c8c8c; font-weight: 400;">台</span>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; color: #8c8c8c; font-size: 13px; font-weight: 500; vertical-align: middle;">
                                <span style="display: inline-block; width: 4px; height: 16px; background-color: #9254de; margin-right: 8px; border-radius: 2px;"></span>
                                创建人
                            </td>
                            <td style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; font-size: 14px; font-weight: 600; color: #262626;">
                                <span style="background-color: #f0f5ff; color: #1890ff; padding: 2px 8px; border-radius: 4px; font-size: 12px;">system</span>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 12px 16px; color: #8c8c8c; font-size: 13px; font-weight: 500; vertical-align: top; padding-top: 16px;">
                                <span style="display: inline-block; width: 4px; height: 16px; background-color: #13c2c2; margin-right: 8px; border-radius: 2px;"></span>
                                触发原因
                            </td>
                            <td style="padding: 12px 16px; font-size: 14px; line-height: 1.6; color: #595959; padding-top: 16px;">
                                <div style="background-color: #fafafa; padding: 12px; border-radius: 4px; border-left: 3px solid #13c2c2;">
                                    策略 [低峰期资源优化] 为集群 [testing-cluster]（worker类型）触发退池操作。<br/>
                                    匹配到 2 台设备（总CPU: 32.0, 总内存: 128.0 GB）。<br/>
                                    预计操作后：<br/>
                                    - CPU分配率将由 45.20% 提升至 58.30%<br/>
                                    - 内存分配率将由 42.10% 提升至 55.60%
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- 涉及设备列表 -->
            <div style="margin-bottom: 24px;">
                <h3 style="color: #262626; margin: 0 0 16px 0; font-size: 18px; font-weight: 600; display: flex; align-items: center;">
                    <span style="margin-right: 8px;">🖥️</span>
                    <span>涉及设备列表</span>
                    <span style="background-color: #1890ff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: 12px; font-weight: 500;">2台</span>
                </h3>
                <div style="background-color: #ffffff; border-radius: 8px; border: 1px solid #e8e8e8; overflow: hidden;">
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background-color: #fafafa;">
                                    <th style="padding: 12px 16px; text-align: left; font-weight: 600; color: #595959; font-size: 13px; border-bottom: 1px solid #e8e8e8;">设备ID</th>
                                    <th style="padding: 12px 16px; text-align: left; font-weight: 600; color: #595959; font-size: 13px; border-bottom: 1px solid #e8e8e8;">CI编码</th>
                                    <th style="padding: 12px 16px; text-align: left; font-weight: 600; color: #595959; font-size: 13px; border-bottom: 1px solid #e8e8e8;">IP地址</th>
                                    <th style="padding: 12px 16px; text-align: left; font-weight: 600; color: #595959; font-size: 13px; border-bottom: 1px solid #e8e8e8;">CPU核心</th>
                                    <th style="padding: 12px 16px; text-align: left; font-weight: 600; color: #595959; font-size: 13px; border-bottom: 1px solid #e8e8e8;">内存(GB)</th>
                                    <th style="padding: 12px 16px; text-align: left; font-weight: 600; color: #595959; font-size: 13px; border-bottom: 1px solid #e8e8e8;">当前状态</th>
                                    <th style="padding: 12px 16px; text-align: left; font-weight: 600; color: #595959; font-size: 13px; border-bottom: 1px solid #e8e8e8;">所属集群</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="background-color: #ffffff;">
                                    <td style="padding: 12px 16px; border-bottom: 1px solid #e8e8e8; font-weight: 600; color: #1890ff; font-family: monospace;">2001</td>
                                    <td style="padding: 12px 16px; border-bottom: 1px solid #e8e8e8; font-family: monospace; font-weight: 500;">SRV-TEST-001</td>
                                    <td style="padding: 12px 16px; border-bottom: 1px solid #e8e8e8; font-family: monospace;">10.200.1.201</td>
                                    <td style="padding: 12px 16px; border-bottom: 1px solid #e8e8e8; font-weight: 600;">16.0</td>
                                    <td style="padding: 12px 16px; border-bottom: 1px solid #e8e8e8; font-weight: 600;">64.0</td>
                                    <td style="padding: 12px 16px; border-bottom: 1px solid #e8e8e8;"><span style="background-color: #ff7a45; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">使用中</span></td>
                                    <td style="padding: 12px 16px; border-bottom: 1px solid #e8e8e8; color: #595959;">testing-cluster</td>
                                </tr>
                                <tr style="background-color: #fafafa;">
                                    <td style="padding: 12px 16px; border-bottom: 1px solid #e8e8e8; font-weight: 600; color: #1890ff; font-family: monospace;">2002</td>
                                    <td style="padding: 12px 16px; border-bottom: 1px solid #e8e8e8; font-family: monospace; font-weight: 500;">SRV-TEST-002</td>
                                    <td style="padding: 12px 16px; border-bottom: 1px solid #e8e8e8; font-family: monospace;">10.200.1.202</td>
                                    <td style="padding: 12px 16px; border-bottom: 1px solid #e8e8e8; font-weight: 600;">16.0</td>
                                    <td style="padding: 12px 16px; border-bottom: 1px solid #e8e8e8; font-weight: 600;">64.0</td>
                                    <td style="padding: 12px 16px; border-bottom: 1px solid #e8e8e8;"><span style="background-color: #ff7a45; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">使用中</span></td>
                                    <td style="padding: 12px 16px; border-bottom: 1px solid #e8e8e8; color: #595959;">testing-cluster</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 操作指引 -->
            <div style="background: linear-gradient(135deg, #1890ff 0%, #0050b3 100%); border-radius: 6px; padding: 20px; margin-bottom: 24px; color: white;">
                <h3 style="margin: 0 0 12px 0; font-size: 16px;">⚡ 操作指引</h3>
                <ul style="margin: 0; padding-left: 20px; line-height: 1.8;">
                    <li>确认集群负载低于阈值且适合执行退池操作</li>
                    <li>发维护邮件并提前对节点做cordon操作（16点前）</li>
                    <li>18点后执行设备的排空（drain）操作</li>
                    <li>将设备从集群中移除</li>
                    <li>视情况更新归还或无须归还设备状态</li>
                </ul>
            </div>

            <!-- 安全提醒 -->
            <div style="border-left: 4px solid #ff7a45; background-color: #fff2e8; padding: 16px; margin-bottom: 24px;">
                <h4 style="color: #d4380d; margin: 0 0 8px 0; font-size: 14px;">⚠️ 安全提醒</h4>
                <p style="color: #ff7a45; font-size: 13px; line-height: 1.6; margin: 0;">
                    请确保在执行退池操作前，<strong>充分验证工作负载已成功迁移</strong>，避免服务中断。建议在低峰期执行此操作。
                </p>
            </div>

            <!-- 联系信息 -->
            <div style="text-align: center; padding: 20px; background-color: #f8f9fa; border-radius: 6px;">
                <p style="color: #666; font-size: 13px; margin: 0 0 8px 0;">
                    如有疑问，请联系技术支持团队：<EMAIL> | 紧急热线：400-800-8888
                </p>
                <p style="color: #999; font-size: 12px; margin: 0;">
                    此邮件由弹性伸缩系统自动发送，请勿直接回复
                </p>
            </div>
        </div>

        <!-- 邮件底部 -->
        <div style="background-color: #f1f3f4; padding: 16px 32px; border-radius: 0 0 8px 8px; text-align: center;">
            <p style="color: #666; font-size: 12px; margin: 0;">
                © 2024 弹性伸缩管理系统 | 发送时间：2024-12-01 16:45:12
            </p>
        </div>
    </div>
</body>
</html> 