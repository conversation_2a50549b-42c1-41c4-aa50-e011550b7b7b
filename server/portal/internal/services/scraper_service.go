package services

import (
	"context"
	"encoding/json"
	"fmt"
	"os/exec"
	"strings"
	"time"
)

// ScrapedTemplate 抓取的模板结构
type ScrapedTemplate struct {
	Title       string            `json:"title"`
	Content     string            `json:"content"`
	URL         string            `json:"url"`
	Metadata    map[string]string `json:"metadata"`
	ScrapedAt   time.Time         `json:"scraped_at"`
	Screenshots []string          `json:"screenshots,omitempty"`
}

// ScrapingOptions 抓取选项
type ScrapingOptions struct {
	URL             string            `json:"url"`
	WaitForSelector string            `json:"wait_for_selector,omitempty"`
	RemoveSelectors []string          `json:"remove_selectors,omitempty"`
	ContentSelector string            `json:"content_selector,omitempty"`
	TitleSelector   string            `json:"title_selector,omitempty"`
	Timeout         int               `json:"timeout,omitempty"` // 秒
	TakeScreenshot  bool              `json:"take_screenshot,omitempty"`
	CustomHeaders   map[string]string `json:"custom_headers,omitempty"`
	UserAgent       string            `json:"user_agent,omitempty"`
	ViewportWidth   int               `json:"viewport_width,omitempty"`
	ViewportHeight  int               `json:"viewport_height,omitempty"`
	WaitTime        int               `json:"wait_time,omitempty"` // 等待时间（毫秒）
}

// ScraperService Playwright抓取服务
type ScraperService struct {
	playwrightPath string
	tempDir        string
}

// NewScraperService 创建新的抓取服务
func NewScraperService() *ScraperService {
	return &ScraperService{
		playwrightPath: "npx playwright",
		tempDir:        "/tmp/sop_scraper",
	}
}

// ScrapeTemplate 抓取模板内容
func (s *ScraperService) ScrapeTemplate(ctx context.Context, options ScrapingOptions) (*ScrapedTemplate, error) {
	// 设置默认值
	if options.Timeout == 0 {
		options.Timeout = 30
	}
	if options.ViewportWidth == 0 {
		options.ViewportWidth = 1280
	}
	if options.ViewportHeight == 0 {
		options.ViewportHeight = 720
	}
	if options.UserAgent == "" {
		options.UserAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
	}

	// 创建Playwright脚本
	script := s.generatePlaywrightScript(options)

	// 执行抓取
	result, err := s.executePlaywrightScript(ctx, script)
	if err != nil {
		return nil, fmt.Errorf("执行抓取脚本失败: %w", err)
	}

	// 解析结果
	template := &ScrapedTemplate{
		URL:       options.URL,
		ScrapedAt: time.Now(),
	}

	if err := json.Unmarshal([]byte(result), template); err != nil {
		return nil, fmt.Errorf("解析抓取结果失败: %w", err)
	}

	return template, nil
}

// generatePlaywrightScript 生成Playwright脚本
func (s *ScraperService) generatePlaywrightScript(options ScrapingOptions) string {
	script := fmt.Sprintf(`
const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext({
    viewport: { width: %d, height: %d },
    userAgent: '%s'
  });
  
  const page = await context.newPage();
  
  try {
    // 设置自定义请求头
    if (%s) {
      await page.setExtraHTTPHeaders(%s);
    }
    
    // 导航到页面
    await page.goto('%s', { waitUntil: 'networkidle', timeout: %d000 });
    
    // 等待特定选择器
    if ('%s') {
      await page.waitForSelector('%s', { timeout: %d000 });
    }
    
    // 等待额外时间
    if (%d > 0) {
      await page.waitForTimeout(%d);
    }
    
    // 移除不需要的元素
    const removeSelectors = %s;
    for (const selector of removeSelectors) {
      await page.evaluate((sel) => {
        const elements = document.querySelectorAll(sel);
        elements.forEach(el => el.remove());
      }, selector);
    }
    
    // 提取内容
    const result = await page.evaluate((contentSelector, titleSelector) => {
      let title = '';
      let content = '';
      
      // 提取标题
      if (titleSelector) {
        const titleEl = document.querySelector(titleSelector);
        title = titleEl ? titleEl.textContent.trim() : '';
      }
      if (!title) {
        const titleEl = document.querySelector('title') || 
                       document.querySelector('h1') || 
                       document.querySelector('h2');
        title = titleEl ? titleEl.textContent.trim() : '';
      }
      
      // 提取内容
      if (contentSelector) {
        const contentEl = document.querySelector(contentSelector);
        content = contentEl ? contentEl.innerHTML : '';
      } else {
        // 默认提取主要内容区域
        const selectors = [
          'main', 'article', '.content', '#content', 
          '.main-content', '.post-content', '.entry-content'
        ];
        for (const sel of selectors) {
          const el = document.querySelector(sel);
          if (el) {
            content = el.innerHTML;
            break;
          }
        }
        if (!content) {
          content = document.body.innerHTML;
        }
      }
      
      // 清理内容
      content = content
        .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
        .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
        .replace(/<!--[\s\S]*?-->/g, '')
        .replace(/\s+/g, ' ')
        .trim();
      
      return {
        title: title,
        content: content,
        metadata: {
          url: window.location.href,
          domain: window.location.hostname,
          timestamp: new Date().toISOString()
        }
      };
    }, '%s', '%s');
    
    // 截图（如果需要）
    let screenshots = [];
    if (%t) {
      const screenshotPath = '/tmp/screenshot_' + Date.now() + '.png';
      await page.screenshot({ path: screenshotPath, fullPage: true });
      screenshots.push(screenshotPath);
    }
    
    result.screenshots = screenshots;
    console.log(JSON.stringify(result));
    
  } catch (error) {
    console.error(JSON.stringify({
      error: error.message,
      title: '',
      content: '',
      metadata: { error: true }
    }));
  } finally {
    await browser.close();
  }
})();
`,
		options.ViewportWidth,
		options.ViewportHeight,
		options.UserAgent,
		len(options.CustomHeaders) > 0,
		s.mapToJSON(options.CustomHeaders),
		options.URL,
		options.Timeout,
		options.WaitForSelector,
		options.WaitForSelector,
		options.Timeout,
		options.WaitTime,
		options.WaitTime,
		s.arrayToJSON(options.RemoveSelectors),
		options.ContentSelector,
		options.TitleSelector,
		options.TakeScreenshot,
	)

	return script
}

// executePlaywrightScript 执行Playwright脚本
func (s *ScraperService) executePlaywrightScript(ctx context.Context, script string) (string, error) {
	// 创建临时脚本文件
	scriptFile := fmt.Sprintf("%s/scrape_%d.js", s.tempDir, time.Now().UnixNano())

	// 确保目录存在
	if err := exec.Command("mkdir", "-p", s.tempDir).Run(); err != nil {
		return "", fmt.Errorf("创建临时目录失败: %w", err)
	}

	// 写入脚本文件
	if err := exec.Command("sh", "-c", fmt.Sprintf("echo '%s' > %s", script, scriptFile)).Run(); err != nil {
		return "", fmt.Errorf("写入脚本文件失败: %w", err)
	}

	// 执行脚本
	cmd := exec.CommandContext(ctx, "node", scriptFile)
	output, err := cmd.Output()

	// 清理临时文件
	exec.Command("rm", "-f", scriptFile).Run()

	if err != nil {
		return "", fmt.Errorf("执行Playwright脚本失败: %w", err)
	}

	return string(output), nil
}

// mapToJSON 将map转换为JSON字符串
func (s *ScraperService) mapToJSON(m map[string]string) string {
	if len(m) == 0 {
		return "{}"
	}

	data, _ := json.Marshal(m)
	return string(data)
}

// arrayToJSON 将数组转换为JSON字符串
func (s *ScraperService) arrayToJSON(arr []string) string {
	if len(arr) == 0 {
		return "[]"
	}

	data, _ := json.Marshal(arr)
	return string(data)
}

// ValidateURL 验证URL格式
func (s *ScraperService) ValidateURL(url string) error {
	if url == "" {
		return fmt.Errorf("URL不能为空")
	}

	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		return fmt.Errorf("URL必须以http://或https://开头")
	}

	return nil
}

// GetSupportedSelectors 获取支持的选择器示例
func (s *ScraperService) GetSupportedSelectors() map[string][]string {
	return map[string][]string{
		"content_selectors": {
			"main", "article", ".content", "#content",
			".main-content", ".post-content", ".entry-content",
			".documentation", ".wiki-content", ".markdown-body",
		},
		"title_selectors": {
			"h1", "h2", ".title", ".page-title",
			"#title", ".post-title", ".entry-title",
		},
		"remove_selectors": {
			"nav", "header", "footer", ".sidebar",
			".advertisement", ".ads", ".social-share",
			".comments", ".related-posts", "script", "style",
		},
	}
}
