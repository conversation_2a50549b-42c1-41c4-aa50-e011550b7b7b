package service

import (
	"context"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/cloudwego/eino/components/model"
	"github.com/cloudwego/eino/schema"
)

// MockAIModel implements a mock AI model for testing and development
type MockAIModel struct{}

// NewMockAIModel creates a new mock AI model instance
func NewMockAIModel() model.ToolCallingChatModel {
	return &MockAIModel{}
}

// MockStreamResponse represents a simple streaming response that matches what the existing code expects
type MockStreamResponse struct {
	Content      string
	ResponseMeta *MockResponseMeta
}

// MockResponseMeta represents response metadata
type MockResponseMeta struct {
	Usage *MockUsage
}

// MockUsage represents token usage information
type MockUsage struct {
	TotalTokens int
}

// MockStreamReader implements a simple stream reader that returns schema.Message
type MockStreamReader struct {
	chunks []string
	index  int
}

// Recv returns the next chunk in the stream as a schema.Message
func (r *MockStreamReader) Recv() (*schema.Message, error) {
	if r.index >= len(r.chunks) {
		return nil, io.EOF
	}

	chunk := r.chunks[r.index]
	r.index++

	// Simulate network delay
	time.Sleep(50 * time.Millisecond)

	return &schema.Message{
		Role:    schema.Assistant,
		Content: chunk,
	}, nil
}

// Stream implements the streaming interface for the mock AI model
func (m *MockAIModel) Stream(ctx context.Context, messages []*schema.Message, opts ...model.Option) (*schema.StreamReader[*schema.Message], error) {
	// Create a StreamReader - not used since we're using mock content generation in the service
	streamReader := &schema.StreamReader[*schema.Message]{}
	// Note: The actual content generation is handled by generateMockContent in the service
	return streamReader, nil
}

// Generate implements the non-streaming interface (not used in our case)
func (m *MockAIModel) Generate(ctx context.Context, messages []*schema.Message, opts ...model.Option) (*schema.Message, error) {
	// Extract the user prompt from messages
	var userPrompt string
	for _, msg := range messages {
		if msg.Role == schema.User {
			userPrompt = msg.Content
			break
		}
	}

	// Generate mock SOP content
	content := m.generateMockSOPContent(userPrompt)

	return &schema.Message{
		Role:    schema.Assistant,
		Content: content,
	}, nil
}

// BindTools implements the tool calling interface (not used for SOP generation)
func (m *MockAIModel) BindTools(tools []*schema.ToolInfo) model.ToolCallingChatModel {
	return m
}

// WithTools implements the tool calling interface (not used for SOP generation)
func (m *MockAIModel) WithTools(tools []*schema.ToolInfo) (model.ToolCallingChatModel, error) {
	return m, nil
}

// generateMockSOPContent generates realistic mock SOP content
func (m *MockAIModel) generateMockSOPContent(prompt string) string {
	// Extract object information from prompt for more realistic content
	objectName := "系统对象"
	if strings.Contains(prompt, "对象名称:") {
		lines := strings.Split(prompt, "\n")
		for _, line := range lines {
			if strings.Contains(line, "对象名称:") {
				parts := strings.Split(line, ":")
				if len(parts) > 1 {
					objectName = strings.TrimSpace(parts[1])
				}
				break
			}
		}
	}

	return fmt.Sprintf(`# %s 标准操作程序

## 1. 概述

本文档描述了针对 **%s** 的标准操作程序，确保操作的安全性、一致性和可追溯性。

## 2. 适用范围

- 适用对象：%s
- 操作环境：生产环境
- 执行人员：经过培训的运维人员

## 3. 前置条件

### 3.1 权限要求
- [ ] 确认具有相应的操作权限
- [ ] 获得必要的审批流程确认
- [ ] 备份相关数据（如适用）

### 3.2 环境检查
- [ ] 确认系统状态正常
- [ ] 检查依赖服务运行状态
- [ ] 验证网络连接正常

## 4. 操作步骤

### 4.1 准备阶段
1. **登录系统**
   - 使用安全的方式登录到目标系统
   - 验证用户权限和访问控制

2. **环境验证**
   - 检查系统状态和服务运行情况
   - 确认磁盘空间和内存使用情况
   - 验证网络连接和依赖服务

### 4.2 执行阶段
1. **备份当前配置**
   - 创建配置文件备份
   - 记录当前系统状态

2. **执行主要操作**
   - 按照预定计划执行操作
   - 实时监控系统状态
   - 记录操作日志

3. **验证操作结果**
   - 验证服务状态和功能
   - 检查系统日志和错误信息
   - 确认操作达到预期效果

### 4.3 完成阶段
1. **清理临时文件**
   - 清理操作过程中产生的临时文件
   - 整理和归档操作日志

2. **更新文档记录**
   - 记录操作时间和结果
   - 更新相关配置文档
   - 通知相关人员操作完成

## 5. 异常处理

### 5.1 常见问题
| 问题描述 | 可能原因 | 解决方案 |
|---------|---------|---------|
| 服务无法启动 | 配置文件错误 | 检查配置文件语法，恢复备份 |
| 权限不足 | 用户权限问题 | 联系管理员获取必要权限 |
| 网络连接失败 | 网络配置问题 | 检查网络配置和防火墙设置 |

### 5.2 回滚程序
如果操作失败，按以下步骤进行回滚：
1. 停止相关服务
2. 恢复备份配置
3. 重启服务
4. 验证系统状态

## 6. 安全注意事项

⚠️ **重要提醒**
- 所有操作必须在授权时间内进行
- 严格按照步骤执行，不得跳过验证环节
- 如遇异常情况，立即停止操作并上报
- 保留完整的操作日志记录

## 7. 验收标准

- [ ] 系统功能正常运行
- [ ] 性能指标符合预期
- [ ] 无异常错误日志
- [ ] 相关监控指标正常

## 8. 文档信息

- **创建时间**：%s
- **版本号**：1.0
- **审核状态**：待审核
- **下次更新**：根据实际情况调整

---

*本文档由AI智能生成，请根据实际情况进行调整和完善。*`,
		objectName, objectName, objectName, time.Now().Format("2006-01-02 15:04:05"))
}

// splitIntoChunks splits content into smaller chunks for streaming
func (m *MockAIModel) splitIntoChunks(content string) []string {
	// Split by sentences and paragraphs for more natural streaming
	lines := strings.Split(content, "\n")
	var chunks []string

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			chunks = append(chunks, "\n")
			continue
		}

		// Split long lines into smaller chunks
		if len(line) > 50 {
			words := strings.Fields(line)
			var currentChunk strings.Builder

			for _, word := range words {
				if currentChunk.Len() > 0 && currentChunk.Len()+len(word)+1 > 50 {
					chunks = append(chunks, currentChunk.String())
					currentChunk.Reset()
				}

				if currentChunk.Len() > 0 {
					currentChunk.WriteString(" ")
				}
				currentChunk.WriteString(word)
			}

			if currentChunk.Len() > 0 {
				chunks = append(chunks, currentChunk.String()+"\n")
			}
		} else {
			chunks = append(chunks, line+"\n")
		}
	}

	return chunks
}
