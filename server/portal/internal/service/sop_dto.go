// Package service provides the business logic for the portal module.
package service

import (
	"navy-ng/models/portal"
	"time"
)

// SOPDocumentQuery defines query parameters for listing SOP documents.
type SOPDocumentQuery struct {
	Page        int    `json:"page" form:"page" binding:"required,min=1" example:"1" swagger:"description=页码"`
	Size        int    `json:"size" form:"size" binding:"required,min=1,max=100" example:"10" swagger:"description=每页数量"`
	Title       string `json:"title" form:"title" swagger:"description=文档标题，模糊查询"`
	Category    string `json:"category" form:"category" swagger:"description=分类，精确查询"`
	Status      string `json:"status" form:"status" swagger:"description=状态，精确查询"`
	Author      string `json:"author" form:"author" swagger:"description=作者，模糊查询"`
	ContentType string `json:"content_type" form:"content_type" swagger:"description=内容类型，精确查询"`
}

// SOPTemplateQuery defines query parameters for listing SOP templates.
type SOPTemplateQuery struct {
	Page       int    `json:"page" form:"page" binding:"required,min=1" example:"1" swagger:"description=页码"`
	Size       int    `json:"size" form:"size" binding:"required,min=1,max=100" example:"10" swagger:"description=每页数量"`
	Name       string `json:"name" form:"name" swagger:"description=模板名称，模糊查询"`
	Category   string `json:"category" form:"category" swagger:"description=分类，精确查询"`
	SourceType string `json:"source_type" form:"source_type" swagger:"description=来源类型，精确查询"`
	IsActive   *bool  `json:"is_active" form:"is_active" swagger:"description=是否激活"`
}

// SOPGenerationHistoryQuery defines query parameters for listing generation history.
type SOPGenerationHistoryQuery struct {
	Page           int    `json:"page" form:"page" binding:"required,min=1" example:"1" swagger:"description=页码"`
	Size           int    `json:"size" form:"size" binding:"required,min=1,max=100" example:"10" swagger:"description=每页数量"`
	DocumentID     int    `json:"document_id" form:"document_id" swagger:"description=文档ID，精确查询"`
	TemplateID     int    `json:"template_id" form:"template_id" swagger:"description=模板ID，精确查询"`
	GenerationType string `json:"generation_type" form:"generation_type" swagger:"description=生成类型，精确查询"`
	Status         string `json:"status" form:"status" swagger:"description=状态，精确查询"`
	GeneratedBy    string `json:"generated_by" form:"generated_by" swagger:"description=生成者，模糊查询"`
}

// SOPDocumentCreateDTO defines the data transfer object for creating SOP documents.
type SOPDocumentCreateDTO struct {
	Title       string             `json:"title" binding:"required" example:"服务器维护SOP" swagger:"description=文档标题"`
	Content     string             `json:"content" binding:"required" swagger:"description=文档内容"`
	ContentType string             `json:"content_type" example:"html" swagger:"description=内容类型: html, markdown"`
	Status      string             `json:"status" example:"draft" swagger:"description=状态: draft, published, archived"`
	Version     string             `json:"version" example:"1.0" swagger:"description=版本号"`
	Category    string             `json:"category" example:"运维" swagger:"description=分类"`
	Tags        []string           `json:"tags" example:"['服务器','维护','运维']" swagger:"description=标签"`
	Author      string             `json:"author" example:"张三" swagger:"description=作者"`
	Description string             `json:"description" swagger:"description=描述"`
	Metadata    portal.SOPMetadata `json:"metadata" swagger:"description=元数据"`
}

// SOPDocumentUpdateDTO defines the data transfer object for updating SOP documents.
type SOPDocumentUpdateDTO struct {
	Title       string             `json:"title" example:"服务器维护SOP" swagger:"description=文档标题"`
	Content     string             `json:"content" swagger:"description=文档内容"`
	ContentType string             `json:"content_type" example:"html" swagger:"description=内容类型: html, markdown"`
	Status      string             `json:"status" example:"published" swagger:"description=状态: draft, published, archived"`
	Version     string             `json:"version" example:"1.1" swagger:"description=版本号"`
	Category    string             `json:"category" example:"运维" swagger:"description=分类"`
	Tags        []string           `json:"tags" example:"['服务器','维护','运维']" swagger:"description=标签"`
	Author      string             `json:"author" example:"张三" swagger:"description=作者"`
	Description string             `json:"description" swagger:"description=描述"`
	Metadata    portal.SOPMetadata `json:"metadata" swagger:"description=元数据"`
}

// SOPTemplateCreateDTO defines the data transfer object for creating SOP templates.
type SOPTemplateCreateDTO struct {
	Name        string              `json:"name" binding:"required" example:"服务器维护模板" swagger:"description=模板名称"`
	Description string              `json:"description" swagger:"description=模板描述"`
	Content     string              `json:"content" binding:"required" swagger:"description=模板内容"`
	ContentType string              `json:"content_type" example:"html" swagger:"description=内容类型: html, markdown"`
	Category    string              `json:"category" example:"运维" swagger:"description=分类"`
	Variables   portal.SOPVariables `json:"variables" swagger:"description=模板变量定义"`
	SourceURL   string              `json:"source_url" swagger:"description=源URL"`
	SourceType  string              `json:"source_type" example:"manual" swagger:"description=来源类型: manual, scraped"`
	IsActive    bool                `json:"is_active" example:"true" swagger:"description=是否激活"`
}

// SOPTemplateUpdateDTO defines the data transfer object for updating SOP templates.
type SOPTemplateUpdateDTO struct {
	Name        string              `json:"name" example:"服务器维护模板" swagger:"description=模板名称"`
	Description string              `json:"description" swagger:"description=模板描述"`
	Content     string              `json:"content" swagger:"description=模板内容"`
	ContentType string              `json:"content_type" example:"html" swagger:"description=内容类型: html, markdown"`
	Category    string              `json:"category" example:"运维" swagger:"description=分类"`
	Variables   portal.SOPVariables `json:"variables" swagger:"description=模板变量定义"`
	SourceURL   string              `json:"source_url" swagger:"description=源URL"`
	SourceType  string              `json:"source_type" example:"manual" swagger:"description=来源类型: manual, scraped"`
	IsActive    *bool               `json:"is_active" example:"true" swagger:"description=是否激活"`
}

// SOPGenerateRequest defines the request for AI-powered SOP generation.
type SOPGenerateRequest struct {
	TemplateID     int                  `json:"template_id" binding:"required" example:"1" swagger:"description=模板ID"`
	ObjectInfo     portal.SOPObjectInfo `json:"object_info" binding:"required" swagger:"description=操作对象信息"`
	SaveAsDocument bool                 `json:"save_as_document" example:"true" swagger:"description=是否保存为文档"`
	DocumentTitle  string               `json:"document_title" example:"生产服务器维护SOP-20240101" swagger:"description=文档标题(如果保存为文档)"`
	AIModel        string               `json:"ai_model" example:"gpt-4" swagger:"description=使用的AI模型"`
}

// SOPTemplateScrapingRequest defines the request for template scraping.
type SOPTemplateScrapingRequest struct {
	URL         string `json:"url" binding:"required" example:"https://wiki.example.com/sop/server-maintenance" swagger:"description=要抓取的URL"`
	Name        string `json:"name" binding:"required" example:"服务器维护模板" swagger:"description=模板名称"`
	Description string `json:"description" swagger:"description=模板描述"`
	Category    string `json:"category" example:"运维" swagger:"description=分类"`
}

// SOPDocumentResponse defines the response structure for a single SOP document.
type SOPDocumentResponse struct {
	ID          int                `json:"id" example:"1" swagger:"description=ID"`
	Title       string             `json:"title" example:"服务器维护SOP" swagger:"description=文档标题"`
	Content     string             `json:"content" swagger:"description=文档内容"`
	ContentType string             `json:"content_type" example:"html" swagger:"description=内容类型"`
	Status      string             `json:"status" example:"published" swagger:"description=状态"`
	Version     string             `json:"version" example:"1.0" swagger:"description=版本号"`
	Category    string             `json:"category" example:"运维" swagger:"description=分类"`
	Tags        []string           `json:"tags" example:"['服务器','维护','运维']" swagger:"description=标签"`
	Author      string             `json:"author" example:"张三" swagger:"description=作者"`
	Description string             `json:"description" swagger:"description=描述"`
	Metadata    portal.SOPMetadata `json:"metadata" swagger:"description=元数据"`
	CreatedAt   time.Time          `json:"created_at" swagger:"description=创建时间"`
	UpdatedAt   time.Time          `json:"updated_at" swagger:"description=更新时间"`
}

// SOPTemplateResponse defines the response structure for a single SOP template.
type SOPTemplateResponse struct {
	ID          int                 `json:"id" example:"1" swagger:"description=ID"`
	Name        string              `json:"name" example:"服务器维护模板" swagger:"description=模板名称"`
	Description string              `json:"description" swagger:"description=模板描述"`
	Content     string              `json:"content" swagger:"description=模板内容"`
	ContentType string              `json:"content_type" example:"html" swagger:"description=内容类型"`
	Category    string              `json:"category" example:"运维" swagger:"description=分类"`
	Variables   portal.SOPVariables `json:"variables" swagger:"description=模板变量定义"`
	SourceURL   string              `json:"source_url" swagger:"description=源URL"`
	SourceType  string              `json:"source_type" example:"manual" swagger:"description=来源类型"`
	IsActive    bool                `json:"is_active" example:"true" swagger:"description=是否激活"`
	UsageCount  int                 `json:"usage_count" example:"5" swagger:"description=使用次数"`
	CreatedAt   time.Time           `json:"created_at" swagger:"description=创建时间"`
	UpdatedAt   time.Time           `json:"updated_at" swagger:"description=更新时间"`
}

// SOPGenerationHistoryResponse defines the response structure for generation history.
type SOPGenerationHistoryResponse struct {
	ID             int                  `json:"id" example:"1" swagger:"description=ID"`
	DocumentID     int                  `json:"document_id" example:"1" swagger:"description=文档ID"`
	DocumentTitle  string               `json:"document_title" example:"服务器维护SOP" swagger:"description=文档标题"`
	TemplateID     int                  `json:"template_id" example:"1" swagger:"description=模板ID"`
	TemplateName   string               `json:"template_name" example:"服务器维护模板" swagger:"description=模板名称"`
	ObjectInfo     portal.SOPObjectInfo `json:"object_info" swagger:"description=操作对象信息"`
	GenerationType string               `json:"generation_type" example:"ai_generated" swagger:"description=生成类型"`
	Status         string               `json:"status" example:"completed" swagger:"description=状态"`
	Progress       int                  `json:"progress" example:"100" swagger:"description=生成进度"`
	ErrorMessage   string               `json:"error_message" swagger:"description=错误信息"`
	GeneratedBy    string               `json:"generated_by" example:"张三" swagger:"description=生成者"`
	Duration       int                  `json:"duration" example:"5000" swagger:"description=生成耗时(毫秒)"`
	TokensUsed     int                  `json:"tokens_used" example:"1500" swagger:"description=使用的Token数量"`
	AIModel        string               `json:"ai_model" example:"gpt-4" swagger:"description=使用的AI模型"`
	CreatedAt      time.Time            `json:"created_at" swagger:"description=创建时间"`
	UpdatedAt      time.Time            `json:"updated_at" swagger:"description=更新时间"`
}

// SOPGenerateStreamResponse defines the streaming response for AI generation.
type SOPGenerateStreamResponse struct {
	Type      string `json:"type" example:"content" swagger:"description=响应类型: content, progress, error, complete"`
	Content   string `json:"content,omitempty" swagger:"description=生成的内容片段"`
	Progress  int    `json:"progress,omitempty" example:"50" swagger:"description=生成进度(0-100)"`
	Error     string `json:"error,omitempty" swagger:"description=错误信息"`
	HistoryID int    `json:"history_id,omitempty" example:"1" swagger:"description=生成历史记录ID"`
}

// SOPScrapingResponse defines the response for template scraping.
type SOPScrapingResponse struct {
	Success     bool   `json:"success" example:"true" swagger:"description=是否成功"`
	Message     string `json:"message" example:"模板抓取成功" swagger:"description=响应消息"`
	TemplateID  int    `json:"template_id,omitempty" example:"1" swagger:"description=创建的模板ID"`
	ScrapedData struct {
		Title   string `json:"title" example:"服务器维护流程" swagger:"description=抓取的标题"`
		Content string `json:"content" swagger:"description=抓取的内容"`
		URL     string `json:"url" example:"https://wiki.example.com/sop/server-maintenance" swagger:"description=源URL"`
	} `json:"scraped_data,omitempty" swagger:"description=抓取的数据"`
}

// PaginatedResponse represents a paginated response with data and metadata.
type PaginatedResponse[T any] struct {
	Data       []T   `json:"data" swagger:"description=数据列表"`
	Total      int64 `json:"total" example:"100" swagger:"description=总记录数"`
	Page       int   `json:"page" example:"1" swagger:"description=当前页码"`
	PageSize   int   `json:"page_size" example:"20" swagger:"description=每页记录数"`
	TotalPages int   `json:"total_pages" example:"5" swagger:"description=总页数"`
}
