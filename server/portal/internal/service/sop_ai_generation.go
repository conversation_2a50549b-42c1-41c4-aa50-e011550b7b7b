package service

import (
	"context"
	"errors"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/cloudwego/eino/schema"
	"go.uber.org/zap"

	"navy-ng/models/portal"
)

// performAIGeneration performs the actual AI generation process.
func (s *SOPService) performAIGeneration(ctx context.Context, historyID int, template *SOPTemplateResponse, req SOPGenerateRequest, stream<PERSON>han chan<- SOPGenerateStreamResponse) {
	defer close(streamChan)

	startTime := time.Now()

	// Update status to generating
	s.updateGenerationStatus(ctx, historyID, "generating", 10, "")

	// Send initial progress
	streamChan <- SOPGenerateStreamResponse{
		Type:      "progress",
		Progress:  10,
		HistoryID: historyID,
	}

	// Build prompt from template and object info
	prompt, err := s.buildPrompt(template, req.ObjectInfo)
	if err != nil {
		s.handleGenerationError(ctx, historyID, streamChan, fmt.Sprintf("Failed to build prompt: %v", err))
		return
	}

	// Update progress
	s.updateGenerationStatus(ctx, historyID, "generating", 20, "")
	streamChan <- SOPGenerateStreamResponse{
		Type:      "progress",
		Progress:  20,
		HistoryID: historyID,
	}

	// Check if AI model is available
	if s.aiModel == nil {
		s.handleGenerationError(ctx, historyID, streamChan, "AI model not configured")
		return
	}

	// Prepare chat messages
	messages := []*schema.Message{
		{
			Role:    schema.System,
			Content: s.getSystemPrompt(),
		},
		{
			Role:    schema.User,
			Content: prompt,
		},
	}

	// Update progress
	s.updateGenerationStatus(ctx, historyID, "generating", 30, "")
	streamChan <- SOPGenerateStreamResponse{
		Type:      "progress",
		Progress:  30,
		HistoryID: historyID,
	}

	// Generate content using AI model
	var generatedContent strings.Builder
	var tokenCount int

	// Update progress
	s.updateGenerationStatus(ctx, historyID, "generating", 50, "")
	streamChan <- SOPGenerateStreamResponse{
		Type:      "progress",
		Progress:  50,
		HistoryID: historyID,
	}

	// Execute AI generation with streaming
	// Temporary: Use mock content generation for testing
	if s.aiModel == nil {
		s.generateMockContent(ctx, historyID, streamChan, prompt)
		return
	}

	sr, err := s.aiModel.Stream(ctx, messages)
	if err != nil {
		s.handleGenerationError(ctx, historyID, streamChan, fmt.Sprintf("AI generation failed: %v", err))
		return
	}

	// Process streaming response
	for {
		msgChunk, recvErr := sr.Recv()
		if errors.Is(recvErr, io.EOF) {
			break
		}
		if recvErr != nil {
			s.handleGenerationError(ctx, historyID, streamChan, fmt.Sprintf("Stream receive failed: %v", recvErr))
			return
		}

		if msgChunk.Content != "" {
			generatedContent.WriteString(msgChunk.Content)

			// Send content chunk to client
			streamChan <- SOPGenerateStreamResponse{
				Type:      "content",
				Content:   msgChunk.Content,
				HistoryID: historyID,
			}
		}

		// Update token count if available
		if msgChunk.ResponseMeta != nil && msgChunk.ResponseMeta.Usage != nil {
			tokenCount = msgChunk.ResponseMeta.Usage.TotalTokens
		}
	}

	// Update progress
	s.updateGenerationStatus(ctx, historyID, "generating", 80, "")
	streamChan <- SOPGenerateStreamResponse{
		Type:      "progress",
		Progress:  80,
		HistoryID: historyID,
	}

	// Process generated content
	finalContent := generatedContent.String()
	if finalContent == "" {
		s.handleGenerationError(ctx, historyID, streamChan, "No content generated")
		return
	}

	// Calculate duration
	duration := time.Since(startTime)

	// Save document if requested
	var documentID int
	if req.SaveAsDocument {
		doc, createErr := s.CreateDocument(ctx, SOPDocumentCreateDTO{
			Title:       req.DocumentTitle,
			Content:     finalContent,
			ContentType: template.ContentType,
			Status:      "draft",
			Version:     "1.0",
			Category:    template.Category,
			Author:      req.ObjectInfo.Operator,
			Description: fmt.Sprintf("Generated from template: %s", template.Name),
		})
		if createErr != nil {
			s.logger.Warn("Failed to save generated document", zap.Error(createErr))
		} else {
			documentID = doc.ID
		}
	}

	// Update generation history with final results
	err = s.db.WithContext(ctx).Model(&portal.SOPGenerationHistory{}).
		Where("id = ?", historyID).
		Updates(map[string]interface{}{
			"document_id": documentID,
			"status":      "completed",
			"progress":    100,
			"duration":    int(duration.Milliseconds()),
			"tokens_used": tokenCount,
			"prompt":      prompt,
		}).Error

	if err != nil {
		s.logger.Error("Failed to update generation history", zap.Error(err))
	}

	// Increment template usage count
	s.db.WithContext(ctx).Model(&portal.SOPTemplate{}).
		Where("id = ?", req.TemplateID).
		UpdateColumn("usage_count", "usage_count + 1")

	// Send completion signal
	streamChan <- SOPGenerateStreamResponse{
		Type:      "complete",
		Progress:  100,
		HistoryID: historyID,
	}
}

// buildPrompt builds the AI prompt from template and object information.
func (s *SOPService) buildPrompt(template *SOPTemplateResponse, objectInfo portal.SOPObjectInfo) (string, error) {
	var promptBuilder strings.Builder

	// Add context information
	promptBuilder.WriteString("请基于以下模板和操作对象信息，生成详细的标准操作程序(SOP)文档：\n\n")

	// Add template information
	promptBuilder.WriteString("## 模板信息\n")
	promptBuilder.WriteString(fmt.Sprintf("模板名称: %s\n", template.Name))
	if template.Description != "" {
		promptBuilder.WriteString(fmt.Sprintf("模板描述: %s\n", template.Description))
	}
	promptBuilder.WriteString(fmt.Sprintf("分类: %s\n", template.Category))
	promptBuilder.WriteString("\n")

	// Add template content
	promptBuilder.WriteString("## 模板内容\n")
	promptBuilder.WriteString(template.Content)
	promptBuilder.WriteString("\n\n")

	// Add object information
	promptBuilder.WriteString("## 操作对象信息\n")
	promptBuilder.WriteString(fmt.Sprintf("目标对象: %s\n", objectInfo.TargetName))

	if objectInfo.BatchInfo != "" {
		promptBuilder.WriteString(fmt.Sprintf("批次信息: %s\n", objectInfo.BatchInfo))
	}
	if objectInfo.Environment != "" {
		promptBuilder.WriteString(fmt.Sprintf("环境: %s\n", objectInfo.Environment))
	}
	if objectInfo.Operator != "" {
		promptBuilder.WriteString(fmt.Sprintf("操作员: %s\n", objectInfo.Operator))
	}
	if objectInfo.Department != "" {
		promptBuilder.WriteString(fmt.Sprintf("部门: %s\n", objectInfo.Department))
	}
	if objectInfo.ContactInfo != "" {
		promptBuilder.WriteString(fmt.Sprintf("联系信息: %s\n", objectInfo.ContactInfo))
	}

	// Add custom parameters
	if len(objectInfo.CustomParameters) > 0 {
		promptBuilder.WriteString("\n### 自定义参数\n")
		for key, value := range objectInfo.CustomParameters {
			promptBuilder.WriteString(fmt.Sprintf("%s: %v\n", key, value))
		}
	}

	// Add template variables if available
	if len(template.Variables.Variables) > 0 {
		promptBuilder.WriteString("\n## 模板变量\n")
		for _, variable := range template.Variables.Variables {
			promptBuilder.WriteString(fmt.Sprintf("- %s (%s): %s\n", variable.Name, variable.Type, variable.Description))
			if variable.DefaultValue != nil {
				promptBuilder.WriteString(fmt.Sprintf("  默认值: %v\n", variable.DefaultValue))
			}
		}
	}

	promptBuilder.WriteString("\n## 生成要求\n")
	promptBuilder.WriteString("1. 请根据模板内容和操作对象信息，生成具体的、可执行的SOP文档\n")
	promptBuilder.WriteString("2. 确保所有步骤都具体明确，包含必要的检查点和注意事项\n")
	promptBuilder.WriteString("3. 根据操作对象的具体信息，替换模板中的占位符和变量\n")
	promptBuilder.WriteString("4. 保持专业的技术文档格式\n")
	promptBuilder.WriteString("5. 如果模板内容是HTML格式，请保持HTML格式；如果是Markdown格式，请保持Markdown格式\n")

	return promptBuilder.String(), nil
}

// getSystemPrompt returns the system prompt for AI generation.
func (s *SOPService) getSystemPrompt() string {
	return `你是一个专业的技术文档编写助手，专门负责生成标准操作程序(SOP)文档。

你的任务是：
1. 根据提供的模板和操作对象信息，生成详细、准确、可执行的SOP文档
2. 确保生成的文档结构清晰，步骤明确，包含必要的安全提示和检查点
3. 根据具体的操作对象信息，个性化定制文档内容
4. 保持专业的技术文档风格，使用准确的技术术语
5. 确保文档的实用性和可操作性

请严格按照用户提供的模板格式和要求生成文档。`
}

// updateGenerationStatus updates the generation status in database.
func (s *SOPService) updateGenerationStatus(ctx context.Context, historyID int, status string, progress int, errorMsg string) {
	updates := map[string]interface{}{
		"status":   status,
		"progress": progress,
	}
	if errorMsg != "" {
		updates["error_message"] = errorMsg
	}

	err := s.db.WithContext(ctx).Model(&portal.SOPGenerationHistory{}).
		Where("id = ?", historyID).
		Updates(updates).Error

	if err != nil {
		s.logger.Error("Failed to update generation status", zap.Error(err))
	}
}

// handleGenerationError handles errors during generation process.
func (s *SOPService) handleGenerationError(ctx context.Context, historyID int, streamChan chan<- SOPGenerateStreamResponse, errorMsg string) {
	s.updateGenerationStatus(ctx, historyID, "failed", 0, errorMsg)

	streamChan <- SOPGenerateStreamResponse{
		Type:      "error",
		Error:     errorMsg,
		HistoryID: historyID,
	}
}

// generateMockContent generates mock SOP content for testing when AI model is not available
func (s *SOPService) generateMockContent(ctx context.Context, historyID int, streamChan chan<- SOPGenerateStreamResponse, prompt string) {
	// Extract object information from prompt for more realistic content
	objectName := "系统对象"
	if strings.Contains(prompt, "对象名称:") {
		lines := strings.Split(prompt, "\n")
		for _, line := range lines {
			if strings.Contains(line, "对象名称:") {
				parts := strings.Split(line, ":")
				if len(parts) > 1 {
					objectName = strings.TrimSpace(parts[1])
				}
				break
			}
		}
	}

	content := fmt.Sprintf(`# %s 标准操作程序

## 1. 概述

本文档描述了针对 **%s** 的标准操作程序，确保操作的安全性、一致性和可追溯性。

## 2. 适用范围

- 适用对象：%s
- 操作环境：生产环境
- 执行人员：经过培训的运维人员

## 3. 前置条件

### 3.1 权限要求
- [ ] 确认具有相应的操作权限
- [ ] 获得必要的审批流程确认
- [ ] 备份相关数据（如适用）

### 3.2 环境检查
- [ ] 确认系统状态正常
- [ ] 检查依赖服务运行状态
- [ ] 验证网络连接正常

## 4. 操作步骤

### 4.1 准备阶段
1. **登录系统**
   - 使用安全的方式登录到目标系统
   - 验证用户权限和访问控制

2. **环境验证**
   - 检查系统状态和服务运行情况
   - 确认磁盘空间和内存使用情况
   - 验证网络连接和依赖服务

### 4.2 执行阶段
1. **备份当前配置**
   - 创建配置文件备份
   - 记录当前系统状态

2. **执行主要操作**
   - 按照预定计划执行操作
   - 实时监控系统状态
   - 记录操作日志

3. **验证操作结果**
   - 验证服务状态和功能
   - 检查系统日志和错误信息
   - 确认操作达到预期效果

### 4.3 完成阶段
1. **清理临时文件**
   - 清理操作过程中产生的临时文件
   - 整理和归档操作日志

2. **更新文档记录**
   - 记录操作时间和结果
   - 更新相关配置文档
   - 通知相关人员操作完成

## 5. 异常处理

### 5.1 常见问题
| 问题描述 | 可能原因 | 解决方案 |
|---------|---------|---------|
| 服务无法启动 | 配置文件错误 | 检查配置文件语法，恢复备份 |
| 权限不足 | 用户权限问题 | 联系管理员获取必要权限 |
| 网络连接失败 | 网络配置问题 | 检查网络配置和防火墙设置 |

### 5.2 回滚程序
如果操作失败，按以下步骤进行回滚：
1. 停止相关服务
2. 恢复备份配置
3. 重启服务
4. 验证系统状态

## 6. 安全注意事项

⚠️ **重要提醒**
- 所有操作必须在授权时间内进行
- 严格按照步骤执行，不得跳过验证环节
- 如遇异常情况，立即停止操作并上报
- 保留完整的操作日志记录

## 7. 验收标准

- [ ] 系统功能正常运行
- [ ] 性能指标符合预期
- [ ] 无异常错误日志
- [ ] 相关监控指标正常

## 8. 文档信息

- **创建时间**：%s
- **版本号**：1.0
- **审核状态**：待审核
- **下次更新**：根据实际情况调整

---

*本文档由AI智能生成，请根据实际情况进行调整和完善。*`,
		objectName, objectName, objectName, time.Now().Format("2006-01-02 15:04:05"))

	// Split content into chunks for streaming
	chunks := s.splitContentIntoChunks(content)

	// Send progress update
	streamChan <- SOPGenerateStreamResponse{
		Type:      "progress",
		Progress:  75,
		HistoryID: historyID,
	}

	// Stream content chunks
	for i, chunk := range chunks {
		// Simulate processing time
		time.Sleep(50 * time.Millisecond)

		streamChan <- SOPGenerateStreamResponse{
			Type:      "content",
			Content:   chunk,
			HistoryID: historyID,
		}

		// Update progress
		progress := 75 + (i+1)*25/len(chunks)
		if progress > 100 {
			progress = 100
		}

		if i%5 == 0 { // Send progress updates every 5 chunks
			streamChan <- SOPGenerateStreamResponse{
				Type:      "progress",
				Progress:  progress,
				HistoryID: historyID,
			}
		}
	}

	// Final completion
	s.updateGenerationStatus(ctx, historyID, "completed", 500, "")

	streamChan <- SOPGenerateStreamResponse{
		Type:      "complete",
		Progress:  100,
		HistoryID: historyID,
	}
}

// splitContentIntoChunks splits content into smaller chunks for streaming
func (s *SOPService) splitContentIntoChunks(content string) []string {
	// Split by sentences and paragraphs for more natural streaming
	lines := strings.Split(content, "\n")
	var chunks []string

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			chunks = append(chunks, "\n")
			continue
		}

		// Split long lines into smaller chunks
		if len(line) > 50 {
			words := strings.Fields(line)
			var currentChunk strings.Builder

			for _, word := range words {
				if currentChunk.Len() > 0 && currentChunk.Len()+len(word)+1 > 50 {
					chunks = append(chunks, currentChunk.String())
					currentChunk.Reset()
				}

				if currentChunk.Len() > 0 {
					currentChunk.WriteString(" ")
				}
				currentChunk.WriteString(word)
			}

			if currentChunk.Len() > 0 {
				chunks = append(chunks, currentChunk.String()+"\n")
			}
		} else {
			chunks = append(chunks, line+"\n")
		}
	}

	return chunks
}
