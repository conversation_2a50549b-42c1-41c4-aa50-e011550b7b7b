package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/cloudwego/eino/components/model"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"navy-ng/models/portal"
)

// SOPService provides SOP-related business logic.
type SOPService struct {
	db      *gorm.DB
	logger  *zap.Logger
	aiModel model.ToolCallingChatModel
}

// NewSOPService creates a new SOP service instance.
func NewSOPService(db *gorm.DB, logger *zap.Logger) *SOPService {
	return &SOPService{
		db:     db,
		logger: logger,
	}
}

// SetAIModel sets the AI model for content generation.
func (s *SOPService) SetAIModel(aiModel model.ToolCallingChatModel) {
	s.aiModel = aiModel
}

// CreateDocument creates a new SOP document.
func (s *SOPService) CreateDocument(ctx context.Context, dto SOPDocumentCreateDTO) (*SOPDocumentResponse, error) {
	// Convert tags to JSON string
	tagsJSON, err := json.Marshal(dto.Tags)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal tags: %w", err)
	}

	document := portal.SOPDocument{
		Title:       dto.Title,
		Content:     dto.Content,
		ContentType: dto.ContentType,
		Status:      dto.Status,
		Version:     dto.Version,
		Category:    dto.Category,
		Tags:        string(tagsJSON),
		Author:      dto.Author,
		Description: dto.Description,
		Metadata:    dto.Metadata,
	}

	// Set defaults
	if document.ContentType == "" {
		document.ContentType = "html"
	}
	if document.Status == "" {
		document.Status = "draft"
	}
	if document.Version == "" {
		document.Version = "1.0"
	}

	if err := s.db.WithContext(ctx).Create(&document).Error; err != nil {
		return nil, fmt.Errorf("failed to create document: %w", err)
	}

	return s.convertDocumentToResponse(document, dto.Tags), nil
}

// GetDocument retrieves a SOP document by ID.
func (s *SOPService) GetDocument(ctx context.Context, id int) (*SOPDocumentResponse, error) {
	var document portal.SOPDocument
	if err := s.db.WithContext(ctx).First(&document, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("document not found")
		}
		return nil, fmt.Errorf("failed to get document: %w", err)
	}

	// Parse tags
	var tags []string
	if document.Tags != "" {
		if err := json.Unmarshal([]byte(document.Tags), &tags); err != nil {
			s.logger.Warn("Failed to parse tags", zap.Error(err))
		}
	}

	return s.convertDocumentToResponse(document, tags), nil
}

// UpdateDocument updates an existing SOP document.
func (s *SOPService) UpdateDocument(ctx context.Context, id int, dto SOPDocumentUpdateDTO) (*SOPDocumentResponse, error) {
	var document portal.SOPDocument
	if err := s.db.WithContext(ctx).First(&document, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("document not found")
		}
		return nil, fmt.Errorf("failed to get document: %w", err)
	}

	// Update fields
	if dto.Title != "" {
		document.Title = dto.Title
	}
	if dto.Content != "" {
		document.Content = dto.Content
	}
	if dto.ContentType != "" {
		document.ContentType = dto.ContentType
	}
	if dto.Status != "" {
		document.Status = dto.Status
	}
	if dto.Version != "" {
		document.Version = dto.Version
	}
	if dto.Category != "" {
		document.Category = dto.Category
	}
	if dto.Author != "" {
		document.Author = dto.Author
	}
	if dto.Description != "" {
		document.Description = dto.Description
	}

	// Update tags if provided
	if dto.Tags != nil {
		tagsJSON, err := json.Marshal(dto.Tags)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal tags: %w", err)
		}
		document.Tags = string(tagsJSON)
	}

	// Update metadata
	document.Metadata = dto.Metadata

	if err := s.db.WithContext(ctx).Save(&document).Error; err != nil {
		return nil, fmt.Errorf("failed to update document: %w", err)
	}

	return s.convertDocumentToResponse(document, dto.Tags), nil
}

// DeleteDocument deletes a SOP document.
func (s *SOPService) DeleteDocument(ctx context.Context, id int) error {
	result := s.db.WithContext(ctx).Delete(&portal.SOPDocument{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete document: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("document not found")
	}
	return nil
}

// ListDocuments retrieves a paginated list of SOP documents.
func (s *SOPService) ListDocuments(ctx context.Context, query SOPDocumentQuery) (*PaginatedResponse[SOPDocumentResponse], error) {
	var documents []portal.SOPDocument
	var total int64

	// Build query
	db := s.db.WithContext(ctx).Model(&portal.SOPDocument{})

	// Apply filters
	if query.Title != "" {
		db = db.Where("title LIKE ?", "%"+query.Title+"%")
	}
	if query.Category != "" {
		db = db.Where("category = ?", query.Category)
	}
	if query.Status != "" {
		db = db.Where("status = ?", query.Status)
	}
	if query.Author != "" {
		db = db.Where("author LIKE ?", "%"+query.Author+"%")
	}
	if query.ContentType != "" {
		db = db.Where("content_type = ?", query.ContentType)
	}

	// Get total count
	if err := db.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count documents: %w", err)
	}

	// Get paginated results
	offset := (query.Page - 1) * query.Size
	if err := db.Offset(offset).Limit(query.Size).Order("updated_at DESC").Find(&documents).Error; err != nil {
		return nil, fmt.Errorf("failed to list documents: %w", err)
	}

	// Convert to response format
	responses := make([]SOPDocumentResponse, len(documents))
	for i, doc := range documents {
		var tags []string
		if doc.Tags != "" {
			if err := json.Unmarshal([]byte(doc.Tags), &tags); err != nil {
				s.logger.Warn("Failed to parse tags", zap.Error(err))
			}
		}
		responses[i] = *s.convertDocumentToResponse(doc, tags)
	}

	return &PaginatedResponse[SOPDocumentResponse]{
		Data:       responses,
		Total:      total,
		Page:       query.Page,
		PageSize:   query.Size,
		TotalPages: int((total + int64(query.Size) - 1) / int64(query.Size)),
	}, nil
}

// CreateTemplate creates a new SOP template.
func (s *SOPService) CreateTemplate(ctx context.Context, dto SOPTemplateCreateDTO) (*SOPTemplateResponse, error) {
	template := portal.SOPTemplate{
		Name:        dto.Name,
		Description: dto.Description,
		Content:     dto.Content,
		ContentType: dto.ContentType,
		Category:    dto.Category,
		Variables:   dto.Variables,
		SourceURL:   dto.SourceURL,
		SourceType:  dto.SourceType,
		IsActive:    dto.IsActive,
		UsageCount:  0,
	}

	// Set defaults
	if template.ContentType == "" {
		template.ContentType = "html"
	}
	if template.SourceType == "" {
		template.SourceType = "manual"
	}

	if err := s.db.WithContext(ctx).Create(&template).Error; err != nil {
		return nil, fmt.Errorf("failed to create template: %w", err)
	}

	return s.convertTemplateToResponse(template), nil
}

// GetTemplate retrieves a SOP template by ID.
func (s *SOPService) GetTemplate(ctx context.Context, id int) (*SOPTemplateResponse, error) {
	var template portal.SOPTemplate
	if err := s.db.WithContext(ctx).First(&template, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("template not found")
		}
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	return s.convertTemplateToResponse(template), nil
}

// convertDocumentToResponse converts a document model to response DTO.
func (s *SOPService) convertDocumentToResponse(doc portal.SOPDocument, tags []string) *SOPDocumentResponse {
	return &SOPDocumentResponse{
		ID:          doc.ID,
		Title:       doc.Title,
		Content:     doc.Content,
		ContentType: doc.ContentType,
		Status:      doc.Status,
		Version:     doc.Version,
		Category:    doc.Category,
		Tags:        tags,
		Author:      doc.Author,
		Description: doc.Description,
		Metadata:    doc.Metadata,
		CreatedAt:   time.Time(doc.CreatedAt),
		UpdatedAt:   time.Time(doc.UpdatedAt),
	}
}

// UpdateTemplate updates an existing SOP template.
func (s *SOPService) UpdateTemplate(ctx context.Context, id int, dto SOPTemplateUpdateDTO) (*SOPTemplateResponse, error) {
	var template portal.SOPTemplate
	if err := s.db.WithContext(ctx).First(&template, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("template not found")
		}
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	// Update fields
	if dto.Name != "" {
		template.Name = dto.Name
	}
	if dto.Description != "" {
		template.Description = dto.Description
	}
	if dto.Content != "" {
		template.Content = dto.Content
	}
	if dto.ContentType != "" {
		template.ContentType = dto.ContentType
	}
	if dto.Category != "" {
		template.Category = dto.Category
	}
	if dto.SourceURL != "" {
		template.SourceURL = dto.SourceURL
	}
	if dto.SourceType != "" {
		template.SourceType = dto.SourceType
	}
	if dto.IsActive != nil {
		template.IsActive = *dto.IsActive
	}

	// Update variables
	template.Variables = dto.Variables

	if err := s.db.WithContext(ctx).Save(&template).Error; err != nil {
		return nil, fmt.Errorf("failed to update template: %w", err)
	}

	return s.convertTemplateToResponse(template), nil
}

// DeleteTemplate deletes a SOP template.
func (s *SOPService) DeleteTemplate(ctx context.Context, id int) error {
	result := s.db.WithContext(ctx).Delete(&portal.SOPTemplate{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete template: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("template not found")
	}
	return nil
}

// ListTemplates retrieves a paginated list of SOP templates.
func (s *SOPService) ListTemplates(ctx context.Context, query SOPTemplateQuery) (*PaginatedResponse[SOPTemplateResponse], error) {
	var templates []portal.SOPTemplate
	var total int64

	// Build query
	db := s.db.WithContext(ctx).Model(&portal.SOPTemplate{})

	// Apply filters
	if query.Name != "" {
		db = db.Where("name LIKE ?", "%"+query.Name+"%")
	}
	if query.Category != "" {
		db = db.Where("category = ?", query.Category)
	}
	if query.SourceType != "" {
		db = db.Where("source_type = ?", query.SourceType)
	}
	if query.IsActive != nil {
		db = db.Where("is_active = ?", *query.IsActive)
	}

	// Get total count
	if err := db.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count templates: %w", err)
	}

	// Get paginated results
	offset := (query.Page - 1) * query.Size
	if err := db.Offset(offset).Limit(query.Size).Order("updated_at DESC").Find(&templates).Error; err != nil {
		return nil, fmt.Errorf("failed to list templates: %w", err)
	}

	// Convert to response format
	responses := make([]SOPTemplateResponse, len(templates))
	for i, template := range templates {
		responses[i] = *s.convertTemplateToResponse(template)
	}

	return &PaginatedResponse[SOPTemplateResponse]{
		Data:       responses,
		Total:      total,
		Page:       query.Page,
		PageSize:   query.Size,
		TotalPages: int((total + int64(query.Size) - 1) / int64(query.Size)),
	}, nil
}

// GenerateSOPContent generates SOP content using AI based on template and object info.
func (s *SOPService) GenerateSOPContent(ctx context.Context, req SOPGenerateRequest, streamChan chan<- SOPGenerateStreamResponse) (*SOPGenerationHistoryResponse, error) {
	// Get template
	template, err := s.GetTemplate(ctx, req.TemplateID)
	if err != nil {
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	// Create generation history record
	history := portal.SOPGenerationHistory{
		TemplateID:     req.TemplateID,
		ObjectInfo:     req.ObjectInfo,
		GenerationType: "ai_generated",
		Status:         "pending",
		Progress:       0,
		GeneratedBy:    req.ObjectInfo.Operator,
		AIModel:        req.AIModel,
	}

	if err := s.db.WithContext(ctx).Create(&history).Error; err != nil {
		return nil, fmt.Errorf("failed to create generation history: %w", err)
	}

	// Start generation process
	go s.performAIGeneration(ctx, history.ID, template, req, streamChan)

	return &SOPGenerationHistoryResponse{
		ID:             history.ID,
		TemplateID:     history.TemplateID,
		TemplateName:   template.Name,
		ObjectInfo:     history.ObjectInfo,
		GenerationType: history.GenerationType,
		Status:         history.Status,
		Progress:       history.Progress,
		GeneratedBy:    history.GeneratedBy,
		AIModel:        history.AIModel,
		CreatedAt:      time.Time(history.CreatedAt),
		UpdatedAt:      time.Time(history.UpdatedAt),
	}, nil
}

// convertTemplateToResponse converts a template model to response DTO.
func (s *SOPService) convertTemplateToResponse(template portal.SOPTemplate) *SOPTemplateResponse {
	return &SOPTemplateResponse{
		ID:          template.ID,
		Name:        template.Name,
		Description: template.Description,
		Content:     template.Content,
		ContentType: template.ContentType,
		Category:    template.Category,
		Variables:   template.Variables,
		SourceURL:   template.SourceURL,
		SourceType:  template.SourceType,
		IsActive:    template.IsActive,
		UsageCount:  template.UsageCount,
		CreatedAt:   time.Time(template.CreatedAt),
		UpdatedAt:   time.Time(template.UpdatedAt),
	}
}
