import { useCallback, useRef, useState } from 'react';

export interface SSEStreamResponse {
  type: 'content' | 'progress' | 'error' | 'complete';
  content?: string;
  progress?: number;
  error?: string;
  history_id?: number;
}

export interface SSEStreamOptions {
  onContent?: (content: string) => void;
  onProgress?: (progress: number) => void;
  onError?: (error: string) => void;
  onComplete?: () => void;
  onStart?: () => void;
}

export interface SSEStreamState {
  isStreaming: boolean;
  progress: number;
  error: string | null;
  accumulatedContent: string;
}

export const useSSEStream = (options: SSEStreamOptions = {}) => {
  const [state, setState] = useState<SSEStreamState>({
    isStreaming: false,
    progress: 0,
    error: null,
    accumulatedContent: ''
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const readerRef = useRef<ReadableStreamDefaultReader<Uint8Array> | null>(null);

  const startStream = useCallback(async (url: string, requestBody: any) => {
    // Cleanup any existing stream
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    setState(prev => ({
      ...prev,
      isStreaming: true,
      progress: 0,
      error: null,
      accumulatedContent: ''
    }));

    options.onStart?.();

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
        },
        body: JSON.stringify(requestBody),
        signal: abortController.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('text/event-stream')) {
        // Handle non-streaming response
        const result = await response.json();
        if (result.content) {
          setState(prev => ({
            ...prev,
            accumulatedContent: result.content,
            progress: 100,
            isStreaming: false
          }));
          options.onContent?.(result.content);
          options.onComplete?.();
        }
        return;
      }

      // Handle SSE streaming response
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Failed to get response reader');
      }

      readerRef.current = reader;
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }

        if (abortController.signal.aborted) {
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        
        // Keep the last incomplete line in buffer
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();
            
            if (data === '[DONE]') {
              setState(prev => ({
                ...prev,
                isStreaming: false,
                progress: 100
              }));
              options.onComplete?.();
              return;
            }

            if (data === '') {
              continue; // Skip empty data lines
            }

            try {
              const parsed: SSEStreamResponse = JSON.parse(data);
              
              switch (parsed.type) {
                case 'content':
                  if (parsed.content) {
                    setState(prev => ({
                      ...prev,
                      accumulatedContent: prev.accumulatedContent + parsed.content
                    }));
                    options.onContent?.(parsed.content);
                  }
                  break;

                case 'progress':
                  if (typeof parsed.progress === 'number') {
                    setState(prev => ({
                      ...prev,
                      progress: parsed.progress!
                    }));
                    options.onProgress?.(parsed.progress);
                  }
                  break;

                case 'error':
                  const errorMsg = parsed.error || 'Unknown streaming error';
                  setState(prev => ({
                    ...prev,
                    isStreaming: false,
                    error: errorMsg
                  }));
                  options.onError?.(errorMsg);
                  return;

                case 'complete':
                  setState(prev => ({
                    ...prev,
                    isStreaming: false,
                    progress: 100
                  }));
                  options.onComplete?.();
                  return;

                default:
                  console.warn('Unknown SSE message type:', parsed.type);
              }
            } catch (parseError) {
              console.warn('Failed to parse SSE data:', data, parseError);
            }
          }
        }
      }

    } catch (error: any) {
      if (error.name === 'AbortError') {
        // Stream was cancelled
        setState(prev => ({
          ...prev,
          isStreaming: false
        }));
        return;
      }

      const errorMsg = error.message || 'Streaming failed';
      setState(prev => ({
        ...prev,
        isStreaming: false,
        error: errorMsg
      }));
      options.onError?.(errorMsg);
    } finally {
      readerRef.current = null;
    }
  }, [options]);

  const stopStream = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    if (readerRef.current) {
      readerRef.current.cancel();
    }
    setState(prev => ({
      ...prev,
      isStreaming: false
    }));
  }, []);

  const resetState = useCallback(() => {
    setState({
      isStreaming: false,
      progress: 0,
      error: null,
      accumulatedContent: ''
    });
  }, []);

  return {
    state,
    startStream,
    stopStream,
    resetState
  };
};

export default useSSEStream;
