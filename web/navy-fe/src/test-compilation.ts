// Test file to check if our SOP components compile correctly
import <PERSON>OP<PERSON>ditor from './components/SOP/SOPEditor';
import ObjectInfoForm from './components/SOP/ObjectInfoForm';
import StreamingProgress from './components/SOP/StreamingProgress';
import TemplateScraper from './components/SOP/TemplateScraper';
import { useSSEStream } from './hooks/useSSEStream';

// This file is just for compilation testing
export const testComponents = {
  SOPEditor,
  ObjectInfoForm,
  StreamingProgress,
  TemplateScraper,
  useSSEStream
};
