/* SOP查看器容器 */
.sop-viewer {
  background: transparent;
  border-radius: 6px;
  overflow: hidden;
}

.sop-viewer-content {
  padding: 16px;
  font-size: 14px;
  line-height: 1.7;
  color: #2c3e50;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.sop-viewer-content .ProseMirror {
  outline: none;
  border: none;
  padding: 0;
  margin: 0;
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* 段落样式 */
.sop-viewer-content .ProseMirror p {
  margin: 0 0 12px 0;
  padding: 0;
  line-height: 1.7;
}

.sop-viewer-content .ProseMirror p:last-child {
  margin-bottom: 0;
}

/* 标题样式 */
.sop-viewer-content .ProseMirror h1,
.sop-viewer-content .ProseMirror h2,
.sop-viewer-content .ProseMirror h3,
.sop-viewer-content .ProseMirror h4,
.sop-viewer-content .ProseMirror h5,
.sop-viewer-content .ProseMirror h6 {
  margin: 24px 0 16px 0;
  font-weight: 600;
  line-height: 1.4;
  color: #1a1a1a;
}

.sop-viewer-content .ProseMirror h1:first-child,
.sop-viewer-content .ProseMirror h2:first-child,
.sop-viewer-content .ProseMirror h3:first-child,
.sop-viewer-content .ProseMirror h4:first-child,
.sop-viewer-content .ProseMirror h5:first-child,
.sop-viewer-content .ProseMirror h6:first-child {
  margin-top: 0;
}

.sop-viewer-content .ProseMirror h1 {
  font-size: 28px;
  border-bottom: 2px solid #eee;
  padding-bottom: 8px;
}

.sop-viewer-content .ProseMirror h2 {
  font-size: 24px;
  border-bottom: 1px solid #eee;
  padding-bottom: 6px;
}

.sop-viewer-content .ProseMirror h3 {
  font-size: 20px;
}

.sop-viewer-content .ProseMirror h4 {
  font-size: 16px;
}

.sop-viewer-content .ProseMirror h5 {
  font-size: 14px;
}

.sop-viewer-content .ProseMirror h6 {
  font-size: 13px;
  color: #666;
}

/* 列表样式 */
.sop-viewer-content .ProseMirror ul,
.sop-viewer-content .ProseMirror ol {
  padding-left: 24px;
  margin: 16px 0;
}

.sop-viewer-content .ProseMirror li {
  margin: 6px 0;
  line-height: 1.7;
}

.sop-viewer-content .ProseMirror li p {
  margin: 0;
}

.sop-viewer-content .ProseMirror ul li {
  list-style-type: disc;
}

.sop-viewer-content .ProseMirror ol li {
  list-style-type: decimal;
}

/* 嵌套列表 */
.sop-viewer-content .ProseMirror ul ul,
.sop-viewer-content .ProseMirror ol ol,
.sop-viewer-content .ProseMirror ul ol,
.sop-viewer-content .ProseMirror ol ul {
  margin: 4px 0;
}

.sop-viewer-content .ProseMirror ul ul li {
  list-style-type: circle;
}

.sop-viewer-content .ProseMirror ul ul ul li {
  list-style-type: square;
}

/* 代码样式 */
.sop-viewer-content .ProseMirror code {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 3px;
  padding: 2px 6px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 85%;
  color: #e83e8c;
}

.sop-viewer-content .ProseMirror pre {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 13px;
  line-height: 1.45;
}

.sop-viewer-content .ProseMirror pre code {
  background: none;
  border: none;
  padding: 0;
  color: inherit;
  font-size: inherit;
}

/* 文本格式样式 */
.sop-viewer-content .ProseMirror strong {
  font-weight: 600;
  color: #1a1a1a;
}

.sop-viewer-content .ProseMirror em {
  font-style: italic;
}

.sop-viewer-content .ProseMirror u {
  text-decoration: underline;
}

.sop-viewer-content .ProseMirror s {
  text-decoration: line-through;
  color: #666;
}

/* 块引用样式 */
.sop-viewer-content .ProseMirror blockquote {
  border-left: 4px solid #dfe2e5;
  margin: 16px 0;
  padding: 12px 20px;
  background: #f8f9fa;
  color: #6a737d;
  font-style: italic;
}

.sop-viewer-content .ProseMirror blockquote p {
  margin: 0;
}

.sop-viewer-content .ProseMirror blockquote p:not(:last-child) {
  margin-bottom: 8px;
}

/* 水平分割线 */
.sop-viewer-content .ProseMirror hr {
  border: none;
  border-top: 2px solid #eee;
  margin: 24px 0;
  height: 0;
}

/* 表格样式 */
.sop-viewer-content .ProseMirror table {
  border-collapse: collapse;
  margin: 16px 0;
  width: 100%;
  border: 1px solid #dfe2e5;
  border-radius: 6px;
  overflow: hidden;
}

.sop-viewer-content .ProseMirror table td,
.sop-viewer-content .ProseMirror table th {
  border: 1px solid #dfe2e5;
  padding: 12px 16px;
  text-align: left;
  vertical-align: top;
}

.sop-viewer-content .ProseMirror table th {
  background: #f6f8fa;
  font-weight: 600;
  color: #24292e;
}

.sop-viewer-content .ProseMirror table tr:nth-child(even) {
  background: #f9f9f9;
}

/* 链接样式 */
.sop-viewer-content .ProseMirror a {
  color: #0366d6;
  text-decoration: none;
}

.sop-viewer-content .ProseMirror a:hover {
  text-decoration: underline;
}

.sop-viewer-content .ProseMirror a:visited {
  color: #6f42c1;
}

/* 图片样式 */
.sop-viewer-content .ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 8px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 选择样式 */
.sop-viewer-content .ProseMirror ::selection {
  background: #b3d4fc;
}

.sop-viewer-content .ProseMirror::-moz-selection {
  background: #b3d4fc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sop-viewer-content {
    padding: 12px;
    font-size: 13px;
  }
  
  .sop-viewer-content .ProseMirror h1 {
    font-size: 24px;
  }
  
  .sop-viewer-content .ProseMirror h2 {
    font-size: 20px;
  }
  
  .sop-viewer-content .ProseMirror h3 {
    font-size: 18px;
  }
  
  .sop-viewer-content .ProseMirror ul,
  .sop-viewer-content .ProseMirror ol {
    padding-left: 20px;
  }
  
  .sop-viewer-content .ProseMirror table {
    font-size: 12px;
  }
  
  .sop-viewer-content .ProseMirror table td,
  .sop-viewer-content .ProseMirror table th {
    padding: 8px 12px;
  }
}

/* 打印样式 */
@media print {
  .sop-viewer-content {
    color: #000;
    background: #fff;
  }
  
  .sop-viewer-content .ProseMirror a {
    color: #000;
    text-decoration: underline;
  }
  
  .sop-viewer-content .ProseMirror blockquote {
    background: none;
    border-left: 4px solid #000;
  }
  
  .sop-viewer-content .ProseMirror table th {
    background: #f0f0f0;
  }
}
