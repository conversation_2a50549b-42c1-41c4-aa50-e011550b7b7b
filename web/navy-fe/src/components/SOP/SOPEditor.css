/* SOP编辑器容器 */
.sop-editor-wrapper {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #ffffff;
  transition: all 0.2s;
  overflow: hidden;
}

.sop-editor-wrapper:hover {
  border-color: #4096ff;
}

.sop-editor-wrapper:focus-within {
  border-color: #4096ff;
  box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
}

.sop-editor-wrapper.disabled {
  background: #f5f5f5;
  border-color: #d9d9d9;
  cursor: not-allowed;
}

.sop-editor-wrapper.disabled:hover {
  border-color: #d9d9d9;
}

/* 工具栏样式 */
.sop-toolbar {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}

.sop-toolbar-button {
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.sop-toolbar-button.active {
  background: #e6f4ff;
  border-color: #91caff;
  color: #1677ff;
}

.sop-toolbar-button:hover:not(:disabled) {
  background: #f5f5f5;
}

.sop-toolbar-button.active:hover:not(:disabled) {
  background: #bae0ff;
}

/* 编辑器内容区域 */
.sop-editor {
  min-height: 200px;
  max-height: 600px;
  overflow-y: auto;
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
  color: rgba(0, 0, 0, 0.88);
  background: transparent;
  border: none;
  outline: none;
}

.sop-editor-wrapper.disabled .sop-editor {
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
}

/* ProseMirror 编辑器样式 */
.sop-editor .ProseMirror {
  outline: none;
  border: none;
  min-height: 180px;
  padding: 0;
  margin: 0;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.sop-editor .ProseMirror p {
  margin: 0 0 12px 0;
  padding: 0;
  line-height: 1.6;
}

.sop-editor .ProseMirror p:last-child {
  margin-bottom: 0;
}

.sop-editor .ProseMirror p.is-empty:first-child::before {
  content: attr(data-placeholder);
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
  float: left;
  height: 0;
}

/* 标题样式 */
.sop-editor .ProseMirror h1,
.sop-editor .ProseMirror h2,
.sop-editor .ProseMirror h3,
.sop-editor .ProseMirror h4,
.sop-editor .ProseMirror h5,
.sop-editor .ProseMirror h6 {
  margin: 20px 0 12px 0;
  font-weight: 600;
  line-height: 1.4;
  color: rgba(0, 0, 0, 0.88);
}

.sop-editor .ProseMirror h1:first-child,
.sop-editor .ProseMirror h2:first-child,
.sop-editor .ProseMirror h3:first-child,
.sop-editor .ProseMirror h4:first-child,
.sop-editor .ProseMirror h5:first-child,
.sop-editor .ProseMirror h6:first-child {
  margin-top: 0;
}

.sop-editor .ProseMirror h1 {
  font-size: 24px;
}

.sop-editor .ProseMirror h2 {
  font-size: 20px;
}

.sop-editor .ProseMirror h3 {
  font-size: 16px;
}

.sop-editor .ProseMirror h4 {
  font-size: 14px;
}

.sop-editor .ProseMirror h5 {
  font-size: 13px;
}

.sop-editor .ProseMirror h6 {
  font-size: 12px;
}

/* 列表样式 */
.sop-editor .ProseMirror ul,
.sop-editor .ProseMirror ol {
  padding-left: 24px;
  margin: 12px 0;
}

.sop-editor .ProseMirror li {
  margin: 4px 0;
  line-height: 1.6;
}

.sop-editor .ProseMirror li p {
  margin: 0;
}

/* 代码样式 */
.sop-editor .ProseMirror code {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 3px;
  padding: 2px 6px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 85%;
  color: #d63384;
}

.sop-editor .ProseMirror pre {
  background: #f6f8fa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
  margin: 12px 0;
  overflow-x: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 13px;
  line-height: 1.45;
}

.sop-editor .ProseMirror pre code {
  background: none;
  border: none;
  padding: 0;
  color: inherit;
  font-size: inherit;
}

/* 文本格式样式 */
.sop-editor .ProseMirror strong {
  font-weight: 600;
}

.sop-editor .ProseMirror em {
  font-style: italic;
}

.sop-editor .ProseMirror u {
  text-decoration: underline;
}

.sop-editor .ProseMirror s {
  text-decoration: line-through;
}

/* 选择样式 */
.sop-editor .ProseMirror ::selection {
  background: #b3d4fc;
}

.sop-editor .ProseMirror::-moz-selection {
  background: #b3d4fc;
}

/* 焦点样式 */
.sop-editor .ProseMirror-focused {
  outline: none;
}

/* 块引用样式 */
.sop-editor .ProseMirror blockquote {
  border-left: 4px solid #d9d9d9;
  margin: 12px 0;
  padding: 8px 16px;
  background: #f9f9f9;
  color: rgba(0, 0, 0, 0.65);
}

.sop-editor .ProseMirror blockquote p {
  margin: 0;
}

/* 水平分割线 */
.sop-editor .ProseMirror hr {
  border: none;
  border-top: 1px solid #d9d9d9;
  margin: 16px 0;
}

/* 表格样式 */
.sop-editor .ProseMirror table {
  border-collapse: collapse !important;
  width: 100% !important;
  margin: 16px 0 !important;
  table-layout: fixed !important;
  overflow: hidden !important;
  border: 2px solid #000 !important;
}

.sop-editor .ProseMirror table td,
.sop-editor .ProseMirror table th {
  border: 2px solid #000 !important;
  padding: 8px 12px !important;
  vertical-align: top !important;
  position: relative !important;
  min-width: 50px !important;
  min-height: 40px !important;
  background-color: #fff !important;
}

.sop-editor .ProseMirror table th {
  font-weight: bold !important;
  background-color: #f0f0f0 !important;
  text-align: left !important;
}

.sop-editor .ProseMirror table td:empty:before,
.sop-editor .ProseMirror table th:empty:before {
  content: "\00a0" !important;
  display: block !important;
}

.sop-editor .ProseMirror .selectedCell:after {
  z-index: 2 !important;
  position: absolute !important;
  content: "" !important;
  left: 0 !important;
  right: 0 !important;
  top: 0 !important;
  bottom: 0 !important;
  background: rgba(30, 144, 255, 0.3) !important;
  pointer-events: none !important;
  border: 2px solid #1890ff !important;
}

.sop-editor .ProseMirror .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: 0;
  width: 4px;
  z-index: 20;
  background-color: #adf;
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sop-toolbar {
    padding: 6px 8px;
  }
  
  .sop-toolbar-button {
    min-width: 28px;
    height: 28px;
  }
  
  .sop-editor {
    padding: 12px;
    font-size: 13px;
  }
  
  .sop-editor .ProseMirror {
    min-height: 150px;
  }
}

/* 滚动条样式 */
.sop-editor::-webkit-scrollbar {
  width: 6px;
}

.sop-editor::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.sop-editor::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.sop-editor::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
