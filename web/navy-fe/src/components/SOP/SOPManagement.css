/* SOP管理页面样式 */
.sop-management {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.sop-management .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.sop-management .ant-table {
  background: #fff;
  border-radius: 6px;
}

.sop-management .ant-table-thead > tr > th {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
}

.sop-management .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 状态标签样式 */
.sop-management .ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
  border: none;
}

/* 操作按钮样式 */
.sop-management .ant-btn-text {
  padding: 4px 8px;
  height: auto;
  border-radius: 4px;
}

.sop-management .ant-btn-text:hover {
  background: #f0f0f0;
}

.sop-management .ant-btn-text.ant-btn-dangerous:hover {
  background: #fff2f0;
  color: #ff4d4f;
}

/* 模态框样式 */
.sop-management .ant-modal {
  top: 20px;
}

.sop-management .ant-modal-content {
  border-radius: 8px;
  overflow: hidden;
}

.sop-management .ant-modal-header {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.sop-management .ant-modal-title {
  font-size: 16px;
  font-weight: 600;
}

.sop-management .ant-modal-body {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.sop-management .ant-modal-footer {
  border-top: 1px solid #f0f0f0;
  padding: 12px 24px;
}

/* 表单样式 */
.sop-management .ant-form-item-label > label {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.88);
}

.sop-management .ant-form-item-label > label.ant-form-item-required::before {
  color: #ff4d4f;
}

.sop-management .ant-input,
.sop-management .ant-select-selector,
.sop-management .ant-input-affix-wrapper {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.2s;
}

.sop-management .ant-input:hover,
.sop-management .ant-select-selector:hover,
.sop-management .ant-input-affix-wrapper:hover {
  border-color: #4096ff;
}

.sop-management .ant-input:focus,
.sop-management .ant-select-focused .ant-select-selector,
.sop-management .ant-input-affix-wrapper-focused {
  border-color: #4096ff;
  box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
}

/* 选择器样式 */
.sop-management .ant-select-dropdown {
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08);
}

.sop-management .ant-select-item {
  border-radius: 4px;
  margin: 2px 8px;
}

.sop-management .ant-select-item-option-selected {
  background: #e6f4ff;
  color: #1677ff;
}

/* 标题样式 */
.sop-management .ant-typography h3 {
  color: rgba(0, 0, 0, 0.88);
  font-weight: 600;
  margin-bottom: 8px;
}

.sop-management .ant-typography h4 {
  color: rgba(0, 0, 0, 0.88);
  font-weight: 500;
  margin-bottom: 16px;
}

/* 分割线样式 */
.sop-management .ant-divider {
  margin: 16px 0;
  border-color: #f0f0f0;
}

/* 空状态样式 */
.sop-management .ant-empty {
  padding: 40px 0;
}

.sop-management .ant-empty-description {
  color: rgba(0, 0, 0, 0.45);
}

/* 加载状态样式 */
.sop-management .ant-spin-container {
  position: relative;
}

.sop-management .ant-spin-blur {
  opacity: 0.5;
  pointer-events: none;
}

/* 消息提示样式 */
.sop-management .ant-message {
  top: 24px;
}

.sop-management .ant-message-notice {
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08);
}

/* 确认框样式 */
.sop-management .ant-popconfirm {
  z-index: 1060;
}

.sop-management .ant-popconfirm-inner {
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sop-management {
    padding: 16px;
  }
  
  .sop-management .ant-modal {
    margin: 0;
    max-width: 100vw;
    top: 0;
  }
  
  .sop-management .ant-modal-content {
    border-radius: 0;
  }
  
  .sop-management .ant-table {
    font-size: 12px;
  }
  
  .sop-management .ant-table-thead > tr > th,
  .sop-management .ant-table-tbody > tr > td {
    padding: 8px;
  }
  
  .sop-management .ant-btn {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
  }
}

@media (max-width: 576px) {
  .sop-management .ant-table-thead > tr > th:nth-child(n+4),
  .sop-management .ant-table-tbody > tr > td:nth-child(n+4) {
    display: none;
  }
  
  .sop-management .ant-space {
    flex-wrap: wrap;
  }
  
  .sop-management .ant-space-item {
    margin-bottom: 8px;
  }
}

/* 打印样式 */
@media print {
  .sop-management {
    background: #fff;
    padding: 0;
  }
  
  .sop-management .ant-card {
    box-shadow: none;
    border: none;
  }
  
  .sop-management .ant-btn,
  .sop-management .ant-modal,
  .sop-management .ant-popconfirm {
    display: none !important;
  }
  
  .sop-management .ant-table {
    border: 1px solid #000;
  }
  
  .sop-management .ant-table-thead > tr > th,
  .sop-management .ant-table-tbody > tr > td {
    border: 1px solid #000;
    color: #000;
  }
}
