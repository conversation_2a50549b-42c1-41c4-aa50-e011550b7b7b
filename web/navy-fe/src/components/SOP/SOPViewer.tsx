import React, { useMemo } from 'react';
import { createEditor } from 'prosekit/core';
import { defineBasicExtension } from 'prosekit/basic';
import { defineReadonly } from 'prosekit/extensions/readonly';
import { union } from 'prosekit/core';
import { ProseKit } from 'prosekit/react';

import 'prosekit/basic/style.css';
import './SOPViewer.css';

interface SOPViewerProps {
  content: string;
  className?: string;
  style?: React.CSSProperties;
}

const SOPViewer: React.FC<SOPViewerProps> = ({ 
  content, 
  className = '',
  style = {}
}) => {
  const editor = useMemo(() => {
    const extension = union(
      defineBasicExtension(),
      defineReadonly() // 设置为只读模式
    );

    return createEditor({
      extension,
      defaultContent: content || '<p>暂无内容</p>',
    });
  }, [content]);

  return (
    <ProseKit editor={editor}>
      <div 
        className={`sop-viewer ${className}`}
        style={style}
      >
        <div 
          ref={editor.mount} 
          className="sop-viewer-content ProseMirror" 
        />
      </div>
    </ProseKit>
  );
};

export default SOPViewer;
