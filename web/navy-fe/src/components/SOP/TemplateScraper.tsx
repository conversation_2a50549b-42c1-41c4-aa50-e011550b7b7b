import React, { useState, useCallback } from 'react';
import {
  Modal,
  Form,
  Input,
  Button,
  Select,
  Switch,
  InputNumber,
  Space,
  Divider,
  Alert,
  Card,
  Tag,
  Tooltip,
  message
} from 'antd';
import {
  GlobalOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  CopyOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
const { Option } = Select;

interface ScrapingOptions {
  url: string;
  wait_for_selector?: string;
  remove_selectors?: string[];
  content_selector?: string;
  title_selector?: string;
  timeout?: number;
  take_screenshot?: boolean;
  custom_headers?: Record<string, string>;
  user_agent?: string;
  viewport_width?: number;
  viewport_height?: number;
  wait_time?: number;
}

interface ScrapedTemplate {
  title: string;
  content: string;
  url: string;
  metadata: Record<string, string>;
  scraped_at: string;
  screenshots?: string[];
}

interface TemplateScrapeProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: (template: ScrapedTemplate) => void;
}

const TemplateScraper: React.FC<TemplateScrapeProps> = ({
  visible,
  onCancel,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [scrapedData, setScrapedData] = useState<ScrapedTemplate | null>(null);
  const [supportedSelectors, setSupportedSelectors] = useState<Record<string, string[]>>({});

  // 获取支持的选择器
  const fetchSupportedSelectors = useCallback(async () => {
    try {
      const response = await fetch('/api/sop/scrape/selectors');
      if (response.ok) {
        const data = await response.json();
        setSupportedSelectors(data.data || {});
      }
    } catch (error) {
      console.error('Failed to fetch supported selectors:', error);
    }
  }, []);

  React.useEffect(() => {
    if (visible) {
      fetchSupportedSelectors();
    }
  }, [visible, fetchSupportedSelectors]);

  const handleScrape = useCallback(async (values: ScrapingOptions) => {
    setLoading(true);
    try {
      const response = await fetch('/api/sop/scrape', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...values,
          remove_selectors: values.remove_selectors?.filter(Boolean) || [],
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Scraping failed');
      }

      const result = await response.json();
      const template = result.data as ScrapedTemplate;
      setScrapedData(template);
      message.success('模板抓取成功！');
    } catch (error) {
      message.error(`抓取失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  }, []);

  const handleUseTemplate = useCallback(() => {
    if (scrapedData) {
      onSuccess(scrapedData);
      setScrapedData(null);
      form.resetFields();
      onCancel();
    }
  }, [scrapedData, onSuccess, form, onCancel]);

  const handleCancel = useCallback(() => {
    setScrapedData(null);
    form.resetFields();
    onCancel();
  }, [form, onCancel]);

  const renderSelectorHelp = (type: string) => {
    const selectors = supportedSelectors[type] || [];
    if (selectors.length === 0) return null;

    return (
      <div style={{ marginTop: 8 }}>
        <div style={{ fontSize: '12px', color: '#666', marginBottom: 4 }}>
          常用选择器：
        </div>
        <Space wrap>
          {selectors.slice(0, 6).map((selector) => (
            <Tag
              key={selector}
              style={{ cursor: 'pointer', fontSize: '11px' }}
              onClick={() => {
                const currentValue = form.getFieldValue(type.replace('_selectors', '_selector')) || '';
                form.setFieldsValue({
                  [type.replace('_selectors', '_selector')]: currentValue ? `${currentValue}, ${selector}` : selector
                });
              }}
            >
              {selector}
            </Tag>
          ))}
        </Space>
      </div>
    );
  };

  return (
    <Modal
      title={
        <Space>
          <GlobalOutlined />
          网页模板抓取
        </Space>
      }
      open={visible}
      onCancel={handleCancel}
      width={800}
      footer={null}
      destroyOnClose
    >
      {!scrapedData ? (
        <Form
          form={form}
          layout="vertical"
          onFinish={handleScrape}
          initialValues={{
            timeout: 30,
            viewport_width: 1280,
            viewport_height: 720,
            take_screenshot: false,
            wait_time: 1000,
          }}
        >
          <Alert
            message="网页抓取说明"
            description="输入目标网页URL，系统将自动提取页面内容并转换为SOP模板。支持自定义选择器来精确提取所需内容。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form.Item
            label="目标网页URL"
            name="url"
            rules={[
              { required: true, message: '请输入网页URL' },
              { type: 'url', message: '请输入有效的URL' }
            ]}
          >
            <Input
              placeholder="https://example.com/sop-template"
              prefix={<GlobalOutlined />}
            />
          </Form.Item>

          <Card
            title={
              <Space>
                <SettingOutlined />
                高级配置
              </Space>
            }
            size="small"
            style={{ marginBottom: 16 }}
          >
            <Form.Item
              label={
                <Space>
                  内容选择器
                  <Tooltip title="CSS选择器，用于指定要提取的主要内容区域">
                    <InfoCircleOutlined />
                  </Tooltip>
                </Space>
              }
              name="content_selector"
            >
              <Input placeholder="main, article, .content, #content" />
              {renderSelectorHelp('content_selectors')}
            </Form.Item>

            <Form.Item
              label={
                <Space>
                  标题选择器
                  <Tooltip title="CSS选择器，用于指定页面标题">
                    <InfoCircleOutlined />
                  </Tooltip>
                </Space>
              }
              name="title_selector"
            >
              <Input placeholder="h1, h2, .title, .page-title" />
              {renderSelectorHelp('title_selectors')}
            </Form.Item>

            <Form.Item
              label={
                <Space>
                  移除选择器
                  <Tooltip title="要移除的元素选择器，用逗号分隔">
                    <InfoCircleOutlined />
                  </Tooltip>
                </Space>
              }
              name="remove_selectors"
            >
              <Select
                mode="tags"
                placeholder="nav, header, footer, .sidebar"
                style={{ width: '100%' }}
              >
                {(supportedSelectors.remove_selectors || []).map(selector => (
                  <Option key={selector} value={selector}>{selector}</Option>
                ))}
              </Select>
              {renderSelectorHelp('remove_selectors')}
            </Form.Item>

            <Form.Item
              label={
                <Space>
                  等待选择器
                  <Tooltip title="等待特定元素出现后再开始抓取">
                    <InfoCircleOutlined />
                  </Tooltip>
                </Space>
              }
              name="wait_for_selector"
            >
              <Input placeholder=".content-loaded, #main-content" />
            </Form.Item>

            <Space style={{ width: '100%' }} size="large">
              <Form.Item label="超时时间(秒)" name="timeout">
                <InputNumber min={5} max={120} style={{ width: 120 }} />
              </Form.Item>

              <Form.Item label="等待时间(毫秒)" name="wait_time">
                <InputNumber min={0} max={10000} style={{ width: 120 }} />
              </Form.Item>

              <Form.Item label="截图" name="take_screenshot" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Space>

            <Space style={{ width: '100%' }} size="large">
              <Form.Item label="视口宽度" name="viewport_width">
                <InputNumber min={800} max={1920} style={{ width: 120 }} />
              </Form.Item>

              <Form.Item label="视口高度" name="viewport_height">
                <InputNumber min={600} max={1080} style={{ width: 120 }} />
              </Form.Item>
            </Space>

            <Form.Item
              label="User Agent"
              name="user_agent"
            >
              <Input placeholder="自定义浏览器标识（可选）" />
            </Form.Item>
          </Card>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                开始抓取
              </Button>
              <Button onClick={handleCancel}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      ) : (
        <div>
          <Alert
            message="抓取成功"
            description={`成功从 ${scrapedData.url} 抓取到模板内容`}
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Card
            title={
              <Space>
                <CheckCircleOutlined style={{ color: '#52c41a' }} />
                抓取结果预览
              </Space>
            }
            extra={
              <Space>
                <Button
                  icon={<CopyOutlined />}
                  onClick={() => {
                    navigator.clipboard.writeText(scrapedData.content);
                    message.success('内容已复制到剪贴板');
                  }}
                >
                  复制内容
                </Button>
                <Button type="primary" onClick={handleUseTemplate}>
                  使用此模板
                </Button>
              </Space>
            }
          >
            <div style={{ marginBottom: 16 }}>
              <strong>标题：</strong> {scrapedData.title || '未提取到标题'}
            </div>
            <div style={{ marginBottom: 16 }}>
              <strong>抓取时间：</strong> {new Date(scrapedData.scraped_at).toLocaleString()}
            </div>
            <Divider />
            <div
              style={{
                maxHeight: 400,
                overflow: 'auto',
                border: '1px solid #d9d9d9',
                padding: 16,
                borderRadius: 6,
                backgroundColor: '#fafafa'
              }}
              dangerouslySetInnerHTML={{ __html: scrapedData.content }}
            />
          </Card>

          <div style={{ marginTop: 16, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setScrapedData(null)}>
                重新抓取
              </Button>
              <Button onClick={handleCancel}>
                取消
              </Button>
              <Button type="primary" onClick={handleUseTemplate}>
                使用此模板
              </Button>
            </Space>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default TemplateScraper;
