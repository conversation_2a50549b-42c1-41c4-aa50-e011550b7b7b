import React, { useState } from 'react';
import SOPEditor from './SOPEditor';

// 简单的测试组件来验证重构后的SOPEditor功能
const TestSOPEditor: React.FC = () => {
  const [content, setContent] = useState('');

  const handleChange = (html: string) => {
    setContent(html);
    console.log('Content changed:', html);
  };

  const handleAIGenerate = async (objectInfo: any) => {
    console.log('AI Generate called with:', objectInfo);
    // 模拟AI生成
    await new Promise(resolve => setTimeout(resolve, 1000));
    setContent('<h1>AI生成的SOP</h1><p>这是AI生成的内容示例</p>');
  };

  const handleSave = () => {
    console.log('Save called with content:', content);
  };

  const handleExport = () => {
    console.log('Export called with content:', content);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>SOP编辑器测试</h2>
      <SOPEditor
        value={content}
        onChange={handleChange}
        placeholder="请输入SOP内容..."
        onAIGenerate={handleAIGenerate}
        onSave={handleSave}
        onExport={handleExport}
      />
      
      <div style={{ marginTop: '20px', padding: '10px', background: '#f5f5f5', borderRadius: '4px' }}>
        <h3>当前内容:</h3>
        <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>{content}</pre>
      </div>
    </div>
  );
};

export default TestSOPEditor;
