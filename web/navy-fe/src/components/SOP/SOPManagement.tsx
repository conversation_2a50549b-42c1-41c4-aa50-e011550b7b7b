import React, { useState, useCallback } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Modal, 
  Form, 
  Input, 
  Select, 
  message, 
  Table, 
  Tag, 
  Popconfirm,
  Row,
  Col,
  Typography,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  FileTextOutlined,
  RobotOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import { SOPEditor, SOPViewer } from './index';
import TemplateScraper from './TemplateScraper';
import './SOPManagement.css';
import StreamingProgress from './StreamingProgress';
import useSSEStream from '../../hooks/useSSEStream';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface SOPDocument {
  id: number;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  author: string;
}

interface SOPTemplate {
  id: number;
  name: string;
  description: string;
  content: string;
}

const SOPManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'documents' | 'templates'>('documents');
  const [documents, setDocuments] = useState<SOPDocument[]>([]);
  const [templates, setTemplates] = useState<SOPTemplate[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);
  const [isScraperVisible, setIsScraperVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<SOPDocument | SOPTemplate | null>(null);
  const [viewingItem, setViewingItem] = useState<SOPDocument | null>(null);
  const [modalType, setModalType] = useState<'document' | 'template'>('document');
  const [form] = Form.useForm();
  const [isStreamingVisible, setIsStreamingVisible] = useState(false);

  // SSE streaming hook
  const { state: streamState, startStream, stopStream, resetState } = useSSEStream({
    onContent: (content: string) => {
      // Append content to the current form content
      const currentContent = form.getFieldValue('content') || '';
      form.setFieldsValue({
        content: currentContent + content
      });
    },
    onProgress: (progress: number) => {
      // Progress is handled by the hook state
    },
    onError: (error: string) => {
      message.error(`生成失败: ${error}`);
    },
    onComplete: () => {
      message.success('SOP内容生成完成！');
      setTimeout(() => {
        setIsStreamingVisible(false);
        resetState();
      }, 2000);
    },
    onStart: () => {
      setIsStreamingVisible(true);
    }
  });

  // 模拟数据
  React.useEffect(() => {
    setDocuments([
      {
        id: 1,
        title: '服务器部署标准操作流程',
        content: '<h1>服务器部署标准操作流程</h1><p>本文档描述了服务器部署的标准操作流程...</p>',
        createdAt: '2024-01-15',
        updatedAt: '2024-01-20',
        author: '张三'
      },
      {
        id: 2,
        title: '数据库备份恢复流程',
        content: '<h1>数据库备份恢复流程</h1><p>本文档描述了数据库备份和恢复的操作流程...</p>',
        createdAt: '2024-01-10',
        updatedAt: '2024-01-18',
        author: '李四'
      }
    ]);

    setTemplates([
      {
        id: 1,
        name: '标准运维流程模板',
        description: '用于创建标准运维操作流程的模板',
        content: '<h1>操作流程标题</h1><h2>1. 准备工作</h2><p>描述准备工作...</p><h2>2. 执行步骤</h2><ol><li>步骤一</li><li>步骤二</li></ol>'
      },
      {
        id: 2,
        name: '故障处理模板',
        description: '用于创建故障处理流程的模板',
        content: '<h1>故障处理流程</h1><h2>1. 故障识别</h2><p>描述故障识别方法...</p><h2>2. 处理步骤</h2><ol><li>紧急处理</li><li>根因分析</li></ol>'
      }
    ]);
  }, []);

  const handleCreateDocument = () => {
    setModalType('document');
    setEditingItem(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleCreateTemplate = () => {
    setModalType('template');
    setEditingItem(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (item: SOPDocument | SOPTemplate, type: 'document' | 'template') => {
    setModalType(type);
    setEditingItem(item);
    setIsModalVisible(true);
    form.setFieldsValue(item);
  };

  const handleView = (document: SOPDocument) => {
    setViewingItem(document);
    setIsViewModalVisible(true);
  };

  const handleDelete = (id: number, type: 'document' | 'template') => {
    if (type === 'document') {
      setDocuments(prev => prev.filter(doc => doc.id !== id));
    } else {
      setTemplates(prev => prev.filter(tpl => tpl.id !== id));
    }
    message.success('删除成功');
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      if (modalType === 'document') {
        if (editingItem) {
          // 更新文档
          setDocuments(prev => prev.map(doc => 
            doc.id === editingItem.id ? { ...doc, ...values, updatedAt: new Date().toISOString().split('T')[0] } : doc
          ));
          message.success('文档更新成功');
        } else {
          // 创建新文档
          const newDoc: SOPDocument = {
            id: Date.now(),
            ...values,
            createdAt: new Date().toISOString().split('T')[0],
            updatedAt: new Date().toISOString().split('T')[0],
            author: '当前用户'
          };
          setDocuments(prev => [...prev, newDoc]);
          message.success('文档创建成功');
        }
      } else {
        if (editingItem) {
          // 更新模板
          setTemplates(prev => prev.map(tpl => 
            tpl.id === editingItem.id ? { ...tpl, ...values } : tpl
          ));
          message.success('模板更新成功');
        } else {
          // 创建新模板
          const newTemplate: SOPTemplate = {
            id: Date.now(),
            ...values
          };
          setTemplates(prev => [...prev, newTemplate]);
          message.success('模板创建成功');
        }
      }
      setIsModalVisible(false);
      setEditingItem(null);
      form.resetFields();
    });
  };

  const handleAIGenerate = useCallback(async (objectInfo: any) => {
    try {
      // Prepare request body
      const requestBody = {
        template_id: 1, // 默认模板ID，后续可以让用户选择
        object_info: objectInfo,
        save_as_document: true,
        document_title: form.getFieldValue('title') || `${objectInfo.name}-SOP-${new Date().toISOString().split('T')[0]}`,
        ai_model: 'gpt-4'
      };

      // Start streaming using the SSE hook
      await startStream('/api/sop/generate', requestBody);
    } catch (error) {
      console.error('AI生成失败:', error);
      message.error('生成失败，请重试');
      throw error;
    }
  }, [form, startStream]);

  const handleSave = useCallback(() => {
    form.validateFields(['title', 'content']).then(() => {
      message.success('内容已保存到草稿');
    }).catch(() => {
      message.error('请完善必填信息后再保存');
    });
  }, [form]);

  const handleExport = useCallback(() => {
    const content = form.getFieldValue('content');
    const title = form.getFieldValue('title') || '未命名文档';

    if (!content) {
      message.error('没有内容可以导出');
      return;
    }

    // 创建HTML文档
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${title}</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
          h1, h2, h3 { color: #333; }
          table { border-collapse: collapse; width: 100%; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
          blockquote { border-left: 4px solid #ddd; margin: 0; padding-left: 16px; color: #666; }
        </style>
      </head>
      <body>
        <h1>${title}</h1>
        ${content}
      </body>
      </html>
    `;

    // 创建下载链接
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    message.success('文档导出成功');
  }, [form]);

  const handleScrapedTemplate = useCallback((scrapedData: any) => {
    // 将抓取的模板数据填充到表单中
    form.setFieldsValue({
      name: scrapedData.title || '从网页抓取的模板',
      content: scrapedData.content,
      description: `从 ${scrapedData.url} 抓取的内容`
    });

    // 设置为模板模式并打开编辑器
    setModalType('template');
    setEditingItem(null);
    setIsModalVisible(true);

    message.success('模板内容已加载，请完善信息后保存');
  }, [form]);

  const documentColumns = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: SOPDocument) => (
        <Space>
          <FileTextOutlined />
          <Text strong>{text}</Text>
        </Space>
      )
    },

    {
      title: '作者',
      dataIndex: 'author',
      key: 'author'
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt'
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: SOPDocument) => (
        <Space>
          <Button 
            type="text" 
            icon={<EyeOutlined />} 
            onClick={() => handleView(record)}
            title="查看"
          />
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record, 'document')}
            title="编辑"
          />
          <Popconfirm
            title="确定要删除这个文档吗？"
            onConfirm={() => handleDelete(record.id, 'document')}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined />}
              title="删除"
            />
          </Popconfirm>
        </Space>
      )
    }
  ];

  const templateColumns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => (
        <Space>
          <FileTextOutlined />
          <Text strong>{text}</Text>
        </Space>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: SOPTemplate) => (
        <Space>
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record, 'template')}
            title="编辑"
          />
          <Popconfirm
            title="确定要删除这个模板吗？"
            onConfirm={() => handleDelete(record.id, 'template')}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined />}
              title="删除"
            />
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="sop-management">
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
          <Col>
            <Title level={3} style={{ margin: 0 }}>
              SOP 标准操作流程管理
            </Title>
            <Text type="secondary">
              管理标准操作流程文档和模板，支持AI智能生成
            </Text>
          </Col>
          <Col>
            <Space>
              <Button
                type={activeTab === 'documents' ? 'primary' : 'default'}
                onClick={() => setActiveTab('documents')}
              >
                文档管理
              </Button>
              <Button
                type={activeTab === 'templates' ? 'primary' : 'default'}
                onClick={() => setActiveTab('templates')}
              >
                模板管理
              </Button>
            </Space>
          </Col>
        </Row>

        <Divider />

        {activeTab === 'documents' && (
          <>
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <Title level={4} style={{ margin: 0 }}>SOP文档</Title>
              </Col>
              <Col>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={handleCreateDocument}
                >
                  创建文档
                </Button>
              </Col>
            </Row>
            <Table
              columns={documentColumns}
              dataSource={documents}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </>
        )}

        {activeTab === 'templates' && (
          <>
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <Title level={4} style={{ margin: 0 }}>SOP模板</Title>
              </Col>
              <Col>
                <Space>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleCreateTemplate}
                  >
                    创建模板
                  </Button>
                  <Button
                    icon={<GlobalOutlined />}
                    onClick={() => setIsScraperVisible(true)}
                  >
                    抓取模板
                  </Button>
                </Space>
              </Col>
            </Row>
            <Table
              columns={templateColumns}
              dataSource={templates}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </>
        )}
      </Card>

      {/* 创建/编辑模态框 */}
      <Modal
        title={`${editingItem ? '编辑' : '创建'}${modalType === 'document' ? 'SOP文档' : 'SOP模板'}`}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingItem(null);
          form.resetFields();
        }}
        width={1000}
        okText="保存"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          {modalType === 'document' ? (
            <>
              <Form.Item
                name="title"
                label="文档标题"
                rules={[{ required: true, message: '请输入文档标题' }]}
              >
                <Input placeholder="请输入文档标题" />
              </Form.Item>
            </>
          ) : (
            <>
              <Form.Item
                name="name"
                label="模板名称"
                rules={[{ required: true, message: '请输入模板名称' }]}
              >
                <Input placeholder="请输入模板名称" />
              </Form.Item>
              <Form.Item
                name="description"
                label="模板描述"
                rules={[{ required: true, message: '请输入模板描述' }]}
              >
                <TextArea rows={3} placeholder="请输入模板描述" />
              </Form.Item>
            </>
          )}
          <Form.Item
            name="content"
            label={
              <Space>
                <span>内容</span>
                <Button 
                  type="link" 
                  size="small" 
                  icon={<RobotOutlined />}
                  style={{ padding: 0 }}
                >
                  AI智能生成
                </Button>
              </Space>
            }
            rules={[{ required: true, message: '请输入内容' }]}
          >
            <div style={{ height: '400px' }}>
              <SOPEditor
                value={form.getFieldValue('content')}
                onChange={(html) => form.setFieldsValue({ content: html })}
                onAIGenerate={handleAIGenerate}
                onSave={handleSave}
                onExport={handleExport}
                placeholder={`请输入${modalType === 'document' ? '文档' : '模板'}内容...`}
              />
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* 查看文档模态框 */}
      <Modal
        title={viewingItem?.title}
        open={isViewModalVisible}
        onCancel={() => {
          setIsViewModalVisible(false);
          setViewingItem(null);
        }}
        footer={null}
        width={800}
      >
        {viewingItem && (
          <SOPViewer content={viewingItem.content} />
        )}
      </Modal>

      <TemplateScraper
        visible={isScraperVisible}
        onCancel={() => setIsScraperVisible(false)}
        onSuccess={handleScrapedTemplate}
      />

      <StreamingProgress
        visible={isStreamingVisible}
        progress={streamState.progress}
        isStreaming={streamState.isStreaming}
        error={streamState.error}
        onCancel={() => {
          setIsStreamingVisible(false);
          resetState();
        }}
        onStop={() => {
          stopStream();
          setIsStreamingVisible(false);
          resetState();
        }}
        title="AI正在生成SOP内容"
        description="请稍候，AI正在根据您提供的对象信息生成专业的标准操作程序文档..."
      />
    </div>
  );
};

export default SOPManagement;
