import React, { useState } from 'react';
import SOPEditor from './SOPEditor';

// 最简单的测试组件
const SimpleTest: React.FC = () => {
  const [content, setContent] = useState('<p>测试内容</p>');

  return (
    <div style={{ padding: '20px' }}>
      <h1>SOPEditor 简单测试</h1>
      <div style={{ border: '1px solid #ccc', borderRadius: '4px', padding: '10px' }}>
        <SOPEditor
          value={content}
          onChange={(html) => {
            console.log('Content changed:', html);
            setContent(html);
          }}
          placeholder="请输入内容..."
        />
      </div>
      
      <div style={{ marginTop: '20px', padding: '10px', background: '#f5f5f5' }}>
        <h3>当前HTML内容:</h3>
        <pre style={{ fontSize: '12px', whiteSpace: 'pre-wrap' }}>{content}</pre>
      </div>
    </div>
  );
};

export default SimpleTest;
