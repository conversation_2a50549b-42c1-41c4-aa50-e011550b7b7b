import React, { useMemo, useCallback, useState } from 'react';
import { createEditor, union } from 'prosekit/core';
import { defineBasicExtension } from 'prosekit/basic';
import { definePlaceholder } from 'prosekit/extensions/placeholder';
import { ProseKit, useDocChange, useEditorDerivedValue } from 'prosekit/react';
import {
  TableHandleRoot,
  TableHandleRowRoot,
  TableHandleRowTrigger,
  TableHandleColumnRoot,
  TableHandleColumnTrigger,
  TableHandlePopoverContent,
  TableHandlePopoverItem
} from 'prosekit/react/table-handle';
import { Button, Tooltip, Divider, Space, message } from 'antd';
import {
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  StrikethroughOutlined,
  CodeOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
  UndoOutlined,
  RedoOutlined,
  RobotOutlined,
  LoadingOutlined,

  LinkOutlined,
  QuestionCircleOutlined,
  LineOutlined,
  ClearOutlined,
  SaveOutlined,
  DownloadOutlined,

} from '@ant-design/icons';

import 'prosekit/basic/style.css';
import 'prosekit/basic/typography.css';
import './SOPEditor.css';
import ObjectInfoForm from './ObjectInfoForm';

// 获取工具栏项目状态的函数
function getToolbarItems(editor: any) {
  return {
    undo: {
      isActive: false,
      canExec: editor.commands.undo.canExec(),
      command: () => editor.commands.undo(),
    },
    redo: {
      isActive: false,
      canExec: editor.commands.redo.canExec(),
      command: () => editor.commands.redo(),
    },
    bold: {
      isActive: editor.marks.bold.isActive(),
      canExec: editor.commands.toggleBold.canExec(),
      command: () => editor.commands.toggleBold(),
    },
    italic: {
      isActive: editor.marks.italic.isActive(),
      canExec: editor.commands.toggleItalic.canExec(),
      command: () => editor.commands.toggleItalic(),
    },
    underline: {
      isActive: editor.marks.underline.isActive(),
      canExec: editor.commands.toggleUnderline.canExec(),
      command: () => editor.commands.toggleUnderline(),
    },
    strike: {
      isActive: editor.marks.strike.isActive(),
      canExec: editor.commands.toggleStrike.canExec(),
      command: () => editor.commands.toggleStrike(),
    },
    code: {
      isActive: editor.marks.code.isActive(),
      canExec: editor.commands.toggleCode.canExec(),
      command: () => editor.commands.toggleCode(),
    },
    heading1: {
      isActive: editor.nodes.heading.isActive({ level: 1 }),
      canExec: editor.commands.toggleHeading.canExec({ level: 1 }),
      command: () => editor.commands.toggleHeading({ level: 1 }),
    },
    heading2: {
      isActive: editor.nodes.heading.isActive({ level: 2 }),
      canExec: editor.commands.toggleHeading.canExec({ level: 2 }),
      command: () => editor.commands.toggleHeading({ level: 2 }),
    },
    heading3: {
      isActive: editor.nodes.heading.isActive({ level: 3 }),
      canExec: editor.commands.toggleHeading.canExec({ level: 3 }),
      command: () => editor.commands.toggleHeading({ level: 3 }),
    },
    bulletList: {
      isActive: editor.nodes.list.isActive({ kind: 'bullet' }),
      canExec: editor.commands.toggleList.canExec({ kind: 'bullet' }),
      command: () => editor.commands.toggleList({ kind: 'bullet' }),
    },
    orderedList: {
      isActive: editor.nodes.list.isActive({ kind: 'ordered' }),
      canExec: editor.commands.toggleList.canExec({ kind: 'ordered' }),
      command: () => editor.commands.toggleList({ kind: 'ordered' }),
    },

    insertHorizontalRule: {
      isActive: false,
      canExec: editor.commands.insertHorizontalRule.canExec(),
      command: () => editor.commands.insertHorizontalRule(),
    },
    addLink: {
      isActive: false,
      canExec: editor.commands.addLink.canExec(),
      command: (href: string) => editor.commands.addLink({ href }),
    },
    insertBlockquote: {
      isActive: false,
      canExec: editor.commands.insertBlockquote.canExec(),
      command: () => editor.commands.insertBlockquote(),
    },

    // 保存editor实例以便在需要时访问
    editor,
  };
}

interface ObjectInfo {
  name: string;
  type: string;
  environment: string;
  description: string;
  operator: string;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  estimated_duration: number;
  prerequisites: string[];
  custom_parameters: any[];
  context: string;
  expected_outcome: string;
  risk_level: 'low' | 'medium' | 'high';
  backup_required: boolean;
  approval_required: boolean;
  scheduled_time?: any;
  tags: string[];
}

interface SOPEditorProps {
  value?: string;
  onChange?: (html: string) => void;
  placeholder?: string;
  onAIGenerate?: (objectInfo: ObjectInfo) => Promise<void>;
  onSave?: () => void;
  onExport?: () => void;
  disabled?: boolean;
  className?: string;
}

// 工具栏按钮组件
const ToolbarButton: React.FC<{
  onClick: () => void;
  isActive?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  title?: string;
  loading?: boolean;
}> = ({ onClick, isActive, disabled, children, title, loading }) => (
  <Tooltip title={title}>
    <Button
      type={isActive ? 'primary' : 'text'}
      size="small"
      onClick={onClick}
      disabled={disabled || loading}
      icon={loading ? <LoadingOutlined /> : children}
      className={`sop-toolbar-button ${isActive ? 'active' : ''}`}
      onMouseDown={(e) => e.preventDefault()}
    />
  </Tooltip>
);

// 内部工具栏组件 - 在ProseKit内部使用
const InternalToolbar: React.FC<{
  onAIGenerateClick?: () => void;
  onSave?: () => void;
  onExport?: () => void;
  disabled?: boolean;
  isGenerating?: boolean;
}> = ({ onAIGenerateClick, onSave, onExport, disabled, isGenerating }) => {
  // 现在可以安全地使用useEditorDerivedValue，因为我们在ProseKit内部
  const items = useEditorDerivedValue(getToolbarItems);

  return (
    <div className="sop-toolbar">
      <Space size="small">
        {/* 撤销/重做 */}
        <ToolbarButton
          onClick={items.undo.command}
          disabled={disabled || !items.undo.canExec}
          title="撤销 (Ctrl+Z)"
        >
          <UndoOutlined />
        </ToolbarButton>
        <ToolbarButton
          onClick={items.redo.command}
          disabled={disabled || !items.redo.canExec}
          title="重做 (Ctrl+Y)"
        >
          <RedoOutlined />
        </ToolbarButton>

        <Divider type="vertical" />

        {/* 文本格式 */}
        <ToolbarButton
          onClick={items.bold.command}
          isActive={items.bold.isActive}
          disabled={disabled || !items.bold.canExec}
          title="加粗 (Ctrl+B)"
        >
          <BoldOutlined />
        </ToolbarButton>
        <ToolbarButton
          onClick={items.italic.command}
          isActive={items.italic.isActive}
          disabled={disabled || !items.italic.canExec}
          title="斜体 (Ctrl+I)"
        >
          <ItalicOutlined />
        </ToolbarButton>
        <ToolbarButton
          onClick={items.underline.command}
          isActive={items.underline.isActive}
          disabled={disabled || !items.underline.canExec}
          title="下划线 (Ctrl+U)"
        >
          <UnderlineOutlined />
        </ToolbarButton>
        <ToolbarButton
          onClick={items.strike.command}
          isActive={items.strike.isActive}
          disabled={disabled || !items.strike.canExec}
          title="删除线"
        >
          <StrikethroughOutlined />
        </ToolbarButton>
        <ToolbarButton
          onClick={items.code.command}
          isActive={items.code.isActive}
          disabled={disabled || !items.code.canExec}
          title="代码"
        >
          <CodeOutlined />
        </ToolbarButton>

        <Divider type="vertical" />

        {/* 标题 */}
        <ToolbarButton
          onClick={items.heading1.command}
          isActive={items.heading1.isActive}
          disabled={disabled || !items.heading1.canExec}
          title="标题1"
        >
          H1
        </ToolbarButton>
        <ToolbarButton
          onClick={items.heading2.command}
          isActive={items.heading2.isActive}
          disabled={disabled || !items.heading2.canExec}
          title="标题2"
        >
          H2
        </ToolbarButton>
        <ToolbarButton
          onClick={items.heading3.command}
          isActive={items.heading3.isActive}
          disabled={disabled || !items.heading3.canExec}
          title="标题3"
        >
          H3
        </ToolbarButton>

        <Divider type="vertical" />

        {/* 列表 */}
        <ToolbarButton
          onClick={items.bulletList.command}
          isActive={items.bulletList.isActive}
          disabled={disabled || !items.bulletList.canExec}
          title="无序列表"
        >
          <UnorderedListOutlined />
        </ToolbarButton>
        <ToolbarButton
          onClick={items.orderedList.command}
          isActive={items.orderedList.isActive}
          disabled={disabled || !items.orderedList.canExec}
          title="有序列表"
        >
          <OrderedListOutlined />
        </ToolbarButton>

        <Divider type="vertical" />

        {/* 高级功能 */}
        <button
          onClick={() => {
            editor.commands.exitTable();
            editor.commands.insertTable({ row: 3, col: 3, header: true });
          }}
          className="px-2 py-1 text-sm border rounded hover:bg-gray-100"
        >
          Add Table
        </button>
        <ToolbarButton
          onClick={() => {
            const url = window.prompt('请输入链接地址:');
            if (url && items.addLink.canExec) {
              items.addLink.command(url);
            }
          }}
          disabled={disabled || !items.addLink.canExec}
          title="插入链接"
        >
          <LinkOutlined />
        </ToolbarButton>
        <ToolbarButton
          onClick={items.insertHorizontalRule.command}
          disabled={disabled || !items.insertHorizontalRule.canExec}
          title="插入分割线"
        >
          <LineOutlined />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => {
            if (items.insertBlockquote.canExec) {
              items.insertBlockquote.command();
            }
          }}
          disabled={disabled || !items.insertBlockquote.canExec}
          title="插入引用"
        >
          <QuestionCircleOutlined />
        </ToolbarButton>

        <Divider type="vertical" />

        {/* 文档操作 */}
        {onSave && (
          <ToolbarButton
            onClick={onSave}
            disabled={disabled}
            title="保存文档"
          >
            <SaveOutlined />
          </ToolbarButton>
        )}
        {onExport && (
          <ToolbarButton
            onClick={onExport}
            disabled={disabled}
            title="导出文档"
          >
            <DownloadOutlined />
          </ToolbarButton>
        )}
        <ToolbarButton
          onClick={() => {
            if (window.confirm('确定要清空所有内容吗？')) {
              items.editor.commands.setContent('<p></p>');
            }
          }}
          disabled={disabled}
          title="清空内容"
        >
          <ClearOutlined />
        </ToolbarButton>

        {/* AI生成按钮 */}
        {onAIGenerateClick && (
          <>
            <Divider type="vertical" />
            <ToolbarButton
              onClick={onAIGenerateClick}
              disabled={disabled}
              loading={isGenerating}
              title="AI智能生成内容"
            >
              <RobotOutlined />
            </ToolbarButton>
          </>
        )}
      </Space>
    </div>
  );
};

const SOPEditor: React.FC<SOPEditorProps> = ({
  value = '',
  onChange,
  placeholder = '请输入SOP内容...',
  onAIGenerate,
  onSave,
  onExport,
  disabled = false,
  className = ''
}) => {
  const [showObjectForm, setShowObjectForm] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  // 创建编辑器实例
  const editor = useMemo(() => {
    const extension = union(
      defineBasicExtension(),
      definePlaceholder({ placeholder })
    );

    return createEditor({
      extension,
      defaultContent: value || '<p></p>',
    });
  }, [placeholder, value]);

  // 监听文档变化
  useDocChange(() => {
    if (onChange && !disabled) {
      const html = editor.getDocHTML();
      onChange(html);
    }
  }, { editor });

  // 处理AI生成
  const handleAIGenerate = useCallback(() => {
    setShowObjectForm(true);
  }, []);

  const handleObjectInfoSubmit = useCallback(async (objectInfo: ObjectInfo) => {
    setIsGenerating(true);
    setShowObjectForm(false);

    try {
      if (onAIGenerate) {
        await onAIGenerate(objectInfo);
        message.success('SOP内容生成成功！');
      }
    } catch (error) {
      message.error('生成失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  }, [onAIGenerate]);

  return (
    <div className={`sop-editor-wrapper ${disabled ? 'disabled' : ''} ${className}`}>
      <style>
        {`
          .sop-editor table {
            border-collapse: collapse !important;
            width: 100% !important;
            margin: 16px 0 !important;
            border: 2px solid #000 !important;
          }
          .sop-editor table td,
          .sop-editor table th {
            border: 2px solid #000 !important;
            padding: 8px 12px !important;
            min-width: 50px !important;
            min-height: 40px !important;
            background-color: #fff !important;
          }
          .sop-editor table th {
            background-color: #f0f0f0 !important;
            font-weight: bold !important;
          }
        `}
      </style>
      <div className="sop-editor">
        <ProseKit editor={editor}>
          <InternalToolbar
            onAIGenerateClick={handleAIGenerate}
            onSave={onSave}
            onExport={onExport}
            disabled={disabled}
            isGenerating={isGenerating}
          />
          <div ref={editor.mount} />
          <TableHandleComponent />
        </ProseKit>
      </div>

      <ObjectInfoForm
        visible={showObjectForm}
        onCancel={() => setShowObjectForm(false)}
        onSubmit={handleObjectInfoSubmit}
        loading={isGenerating}
      />
    </div>
  );
};

// TableHandle组件，按照ProseKit官方示例实现
function getTableHandleState(editor: any) {
  return {
    addTableColumnBefore: {
      canExec: editor.commands.addTableColumnBefore.canExec(),
      command: () => editor.commands.addTableColumnBefore(),
    },
    addTableColumnAfter: {
      canExec: editor.commands.addTableColumnAfter.canExec(),
      command: () => editor.commands.addTableColumnAfter(),
    },
    deleteCellSelection: {
      canExec: editor.commands.deleteCellSelection.canExec(),
      command: () => editor.commands.deleteCellSelection(),
    },
    deleteTableColumn: {
      canExec: editor.commands.deleteTableColumn.canExec(),
      command: () => editor.commands.deleteTableColumn(),
    },
    addTableRowAbove: {
      canExec: editor.commands.addTableRowAbove.canExec(),
      command: () => editor.commands.addTableRowAbove(),
    },
    addTableRowBelow: {
      canExec: editor.commands.addTableRowBelow.canExec(),
      command: () => editor.commands.addTableRowBelow(),
    },
    deleteTableRow: {
      canExec: editor.commands.deleteTableRow.canExec(),
      command: () => editor.commands.deleteTableRow(),
    },
  }
}

function TableHandle() {
  const state = useEditorDerivedValue(getTableHandleState)

  return (
    <TableHandleRoot className="contents">
      <TableHandleColumnRoot className="flex items-center box-border justify-center h-[1.2em] w-[1.5em] bg-white hover:bg-gray-100 dark:hover:bg-gray-800 rounded text-gray-500/50 dark:text-gray-500/50 translate-y-3 border border-gray-200 dark:border-gray-800 border-solid [&:not([data-state])]:hidden will-change-transform data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=open]:fade-in-0 data-[state=closed]:fade-out-0 data-[state=open]:zoom-in-95 data-[state=closed]:zoom-out-95 data-[state=open]:animate-duration-150 data-[state=closed]:animate-duration-200">
        <TableHandleColumnTrigger>
          <div className="i-lucide-grip-horizontal h-5 w-5"></div>
        </TableHandleColumnTrigger>
        <TableHandlePopoverContent className="relative block max-h-[25rem] min-w-[8rem] select-none overflow-auto whitespace-nowrap p-1 z-10 box-border rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-950 shadow-lg [&:not([data-state])]:hidden">
          {state.addTableColumnBefore.canExec && (
            <TableHandlePopoverItem
              className="relative min-w-[8rem] scroll-my-1 rounded px-3 py-1.5 flex items-center justify-between gap-8 data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 hover:data-[disabled=true]:opacity-50 box-border cursor-default select-none whitespace-nowrap outline-none data-[focused]:bg-gray-100 dark:data-[focused]:bg-gray-800"
              onSelect={state.addTableColumnBefore.command}
            >
              <span>Insert Left</span>
            </TableHandlePopoverItem>
          )}
          {state.addTableColumnAfter.canExec && (
            <TableHandlePopoverItem
              className="relative min-w-[8rem] scroll-my-1 rounded px-3 py-1.5 flex items-center justify-between gap-8 data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 hover:data-[disabled=true]:opacity-50 box-border cursor-default select-none whitespace-nowrap outline-none data-[focused]:bg-gray-100 dark:data-[focused]:bg-gray-800"
              onSelect={state.addTableColumnAfter.command}
            >
              <span>在右侧插入列</span>
            </TableHandlePopoverItem>
          )}
          {state.deleteCellSelection.canExec && (
            <TableHandlePopoverItem
              className="relative min-w-[8rem] scroll-my-1 rounded px-3 py-1.5 flex items-center justify-between gap-8 data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 hover:data-[disabled=true]:opacity-50 box-border cursor-default select-none whitespace-nowrap outline-none data-[focused]:bg-gray-100 dark:data-[focused]:bg-gray-800"
              onSelect={state.deleteCellSelection.command}
            >
              <span>清空内容</span>
            </TableHandlePopoverItem>
          )}
          {state.deleteTableColumn.canExec && (
            <TableHandlePopoverItem
              className="relative min-w-[8rem] scroll-my-1 rounded px-3 py-1.5 flex items-center justify-between gap-8 data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 hover:data-[disabled=true]:opacity-50 box-border cursor-default select-none whitespace-nowrap outline-none data-[focused]:bg-gray-100 dark:data-[focused]:bg-gray-800"
              onSelect={state.deleteTableColumn.command}
            >
              <span>删除列</span>
            </TableHandlePopoverItem>
          )}
        </TableHandlePopoverContent>
      </TableHandleColumnRoot>
      <TableHandleRowRoot className="flex items-center box-border justify-center h-[1.5em] w-[1.2em] bg-white hover:bg-gray-100 dark:hover:bg-gray-800 rounded text-gray-500/50 dark:text-gray-500/50 translate-x-3 border border-gray-200 dark:border-gray-800 border-solid [&:not([data-state])]:hidden will-change-transform data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=open]:fade-in-0 data-[state=closed]:fade-out-0 data-[state=open]:zoom-in-95 data-[state=closed]:zoom-out-95 data-[state=open]:animate-duration-150 data-[state=closed]:animate-duration-200">
        <TableHandleRowTrigger>
          <div className="h-5 w-5">⋯</div>
        </TableHandleRowTrigger>
        <TableHandlePopoverContent className="relative block max-h-[25rem] min-w-[8rem] select-none overflow-auto whitespace-nowrap p-1 z-10 box-border rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-950 shadow-lg [&:not([data-state])]:hidden">
          {state.addTableRowAbove.canExec && (
            <TableHandlePopoverItem
              className="relative min-w-[8rem] scroll-my-1 rounded px-3 py-1.5 flex items-center justify-between gap-8 data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 hover:data-[disabled=true]:opacity-50 box-border cursor-default select-none whitespace-nowrap outline-none data-[focused]:bg-gray-100 dark:data-[focused]:bg-gray-800"
              onSelect={state.addTableRowAbove.command}
            >
              <span>在上方插入行</span>
            </TableHandlePopoverItem>
          )}
          {state.addTableRowBelow.canExec && (
            <TableHandlePopoverItem
              className="relative min-w-[8rem] scroll-my-1 rounded px-3 py-1.5 flex items-center justify-between gap-8 data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 hover:data-[disabled=true]:opacity-50 box-border cursor-default select-none whitespace-nowrap outline-none data-[focused]:bg-gray-100 dark:data-[focused]:bg-gray-800"
              onSelect={state.addTableRowBelow.command}
            >
              <span>在下方插入行</span>
            </TableHandlePopoverItem>
          )}
          {state.deleteCellSelection.canExec && (
            <TableHandlePopoverItem
              className="relative min-w-[8rem] scroll-my-1 rounded px-3 py-1.5 flex items-center justify-between gap-8 data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 hover:data-[disabled=true]:opacity-50 box-border cursor-default select-none whitespace-nowrap outline-none data-[focused]:bg-gray-100 dark:data-[focused]:bg-gray-800"
              onSelect={state.deleteCellSelection.command}
            >
              <span>清空内容</span>
            </TableHandlePopoverItem>
          )}
          {state.deleteTableRow.canExec && (
            <TableHandlePopoverItem
              className="relative min-w-[8rem] scroll-my-1 rounded px-3 py-1.5 flex items-center justify-between gap-8 data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 hover:data-[disabled=true]:opacity-50 box-border cursor-default select-none whitespace-nowrap outline-none data-[focused]:bg-gray-100 dark:data-[focused]:bg-gray-800"
              onSelect={state.deleteTableRow.command}
            >
              <span>删除行</span>
            </TableHandlePopoverItem>
          )}
        </TableHandlePopoverContent>
      </TableHandleRowRoot>
    </TableHandleRoot>
  );
}

export default SOPEditor;
