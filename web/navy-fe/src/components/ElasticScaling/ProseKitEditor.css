.prosekit-editor-wrapper {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: border-color 0.3s;
  background: #fff;
  overflow: hidden;
}

.prosekit-editor-wrapper:hover {
  border-color: #4096ff;
}

.prosekit-editor-wrapper:focus-within {
  border-color: #4096ff;
  box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.2);
  outline: none;
}

/* 工具栏样式 */
.prosekit-toolbar {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  background: rgba(250, 250, 250, 0.85);
  backdrop-filter: blur(8px);
  gap: 4px;
  flex-wrap: wrap;
  transition: background-color 0.2s ease, backdrop-filter 0.2s ease;
}

.prosekit-toolbar:hover {
  background: rgba(250, 250, 250, 0.95);
  backdrop-filter: blur(12px);
}

.toolbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  padding: 4px 8px;
  border: 1px solid transparent;
  border-radius: 4px;
  background: transparent;
  color: #595959;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  user-select: none;
}

.toolbar-button:hover:not(.disabled) {
  background: #e6f4ff;
  border-color: #91caff;
  color: #1677ff;
}

.toolbar-button.active {
  background: #1677ff;
  border-color: #1677ff;
  color: #fff;
}

.toolbar-button.active:hover {
  background: #4096ff;
  border-color: #4096ff;
}

.toolbar-button.disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.toolbar-separator {
  width: 1px;
  height: 20px;
  background: #d9d9d9;
  margin: 0 4px;
}

.prosekit-editor {
  min-height: 100px;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.5715;
  color: rgba(0, 0, 0, 0.88);
  background: transparent;
  border: none;
  outline: none;
}

/* ProseMirror 编辑器样式 */
.prosekit-editor .ProseMirror {
  outline: none;
  border: none;
  min-height: 80px;
  padding: 0;
  margin: 0;
}

.prosekit-editor .ProseMirror p {
  margin: 0 0 12px 0;
  padding: 0;
}

.prosekit-editor .ProseMirror p:last-child {
  margin-bottom: 0;
}

.prosekit-editor .ProseMirror h4,
.prosekit-editor .ProseMirror h5 {
  margin: 16px 0 8px 0;
  font-weight: 600;
}

.prosekit-editor .ProseMirror h4:first-child,
.prosekit-editor .ProseMirror h5:first-child {
  margin-top: 0;
}

.prosekit-editor .ProseMirror h4 {
  font-size: 16px;
}

.prosekit-editor .ProseMirror h5 {
  font-size: 14px;
}

.prosekit-editor .ProseMirror ul,
.prosekit-editor .ProseMirror ol {
  padding-left: 24px;
  margin: 8px 0;
}

.prosekit-editor .ProseMirror li {
  margin: 4px 0;
}

.prosekit-editor .ProseMirror code {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 3px;
  padding: 2px 4px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 85%;
}

.prosekit-editor .ProseMirror p.is-empty::before {
  content: attr(data-placeholder);
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
  float: left;
  height: 0;
}

/* 确保可以聚焦和选择 */
.prosekit-editor .ProseMirror-focused {
  outline: none;
}

/* 选择样式 */
.prosekit-editor .ProseMirror ::selection {
  background: #b3d4fc;
}

.prosekit-editor .ProseMirror ::-moz-selection {
  background: #b3d4fc;
} 