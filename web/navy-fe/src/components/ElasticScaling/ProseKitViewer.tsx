import React, { useMemo } from 'react';
import { createEditor } from 'prosekit/core';
import { defineBasicExtension } from 'prosekit/basic';
import { defineReadonly } from 'prosekit/extensions/readonly';
import { union } from 'prosekit/core';
import { ProseKit } from 'prosekit/react';

import 'prosekit/basic/style.css';
import './ProseKitViewer.css';

interface ProseKitViewerProps {
  content: string;
}

const ProseKitViewer: React.FC<ProseKitViewerProps> = ({ content }) => {
  const editor = useMemo(() => {
    const extension = union(
      defineBasicExtension(),
      defineReadonly() // 设置为只读模式
    );

    return createEditor({
      extension,
      defaultContent: content || '<p></p>',
    });
  }, [content]);

  return (
    <ProseKit editor={editor}>
      <div className="prosekit-viewer">
        <div ref={editor.mount} className="prosekit-content ProseMirror" />
      </div>
    </ProseKit>
  );
};

export default ProseKitViewer; 