import React, { useMemo } from 'react';
import { createEditor } from 'prosekit/core';
import { defineBasicExtension } from 'prosekit/basic';
import { definePlaceholder } from 'prosekit/extensions/placeholder';
import { union } from 'prosekit/core';
import { ProseKit, useDocChange } from 'prosekit/react';

import 'prosekit/basic/style.css';
import './ProseKitEditor.css';

interface ProseKitEditorProps {
  value?: string;
  onChange?: (html: string) => void;
  placeholder?: string;
}

// 工具栏按钮组件
const ToolbarButton: React.FC<{
  onClick: () => void;
  isActive?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  title?: string;
}> = ({ onClick, isActive, disabled, children, title }) => (
  <button
    type="button"
    onClick={onClick}
    disabled={disabled}
    title={title}
    className={`toolbar-button ${isActive ? 'active' : ''} ${disabled ? 'disabled' : ''}`}
    onMouseDown={(e) => e.preventDefault()}
  >
    {children}
  </button>
);

// 工具栏组件
const Toolbar: React.FC<{ editor: any }> = ({ editor }) => {
  return (
    <div className="prosekit-toolbar">
      <ToolbarButton
        onClick={() => editor.commands.undo()}
        disabled={!editor.commands.undo.canExec()}
        title="撤销"
      >
        ↶
      </ToolbarButton>
      <ToolbarButton
        onClick={() => editor.commands.redo()}
        disabled={!editor.commands.redo.canExec()}
        title="重做"
      >
        ↷
      </ToolbarButton>
      
      <div className="toolbar-separator" />
      
      <ToolbarButton
        onClick={() => editor.commands.toggleBold()}
        isActive={editor.marks.bold.isActive()}
        disabled={!editor.commands.toggleBold.canExec()}
        title="加粗"
      >
        <strong>B</strong>
      </ToolbarButton>
      <ToolbarButton
        onClick={() => editor.commands.toggleItalic()}
        isActive={editor.marks.italic.isActive()}
        disabled={!editor.commands.toggleItalic.canExec()}
        title="斜体"
      >
        <em>I</em>
      </ToolbarButton>
      <ToolbarButton
        onClick={() => editor.commands.toggleUnderline()}
        isActive={editor.marks.underline.isActive()}
        disabled={!editor.commands.toggleUnderline.canExec()}
        title="下划线"
      >
        <u>U</u>
      </ToolbarButton>
      <ToolbarButton
        onClick={() => editor.commands.toggleStrike()}
        isActive={editor.marks.strike.isActive()}
        disabled={!editor.commands.toggleStrike.canExec()}
        title="删除线"
      >
        <s>S</s>
      </ToolbarButton>
      <ToolbarButton
        onClick={() => editor.commands.toggleCode()}
        isActive={editor.marks.code.isActive()}
        disabled={!editor.commands.toggleCode.canExec()}
        title="代码"
      >
        &lt;&gt;
      </ToolbarButton>
      
      <div className="toolbar-separator" />
      
      <ToolbarButton
        onClick={() => editor.commands.toggleHeading({ level: 4 })}
        isActive={editor.nodes.heading.isActive({ level: 4 })}
        disabled={!editor.commands.toggleHeading.canExec({ level: 4 })}
        title="标题4"
      >
        H4
      </ToolbarButton>
      <ToolbarButton
        onClick={() => editor.commands.toggleHeading({ level: 5 })}
        isActive={editor.nodes.heading.isActive({ level: 5 })}
        disabled={!editor.commands.toggleHeading.canExec({ level: 5 })}
        title="标题5"
      >
        H5
      </ToolbarButton>
      
      <div className="toolbar-separator" />
      
      <ToolbarButton
        onClick={() => editor.commands.toggleList({ kind: 'bullet' })}
        isActive={editor.nodes.list.isActive({ kind: 'bullet' })}
        disabled={!editor.commands.toggleList.canExec({ kind: 'bullet' })}
        title="无序列表"
      >
        •••
      </ToolbarButton>
      <ToolbarButton
        onClick={() => editor.commands.toggleList({ kind: 'ordered' })}
        isActive={editor.nodes.list.isActive({ kind: 'ordered' })}
        disabled={!editor.commands.toggleList.canExec({ kind: 'ordered' })}
        title="有序列表"
      >
        123
      </ToolbarButton>
    </div>
  );
};

const ProseKitEditor: React.FC<ProseKitEditorProps> = ({
  value = '',
  onChange,
  placeholder = '请输入描述信息...'
}) => {
  // 使用 useMemo 创建编辑器实例，如官方示例所示
  const editor = useMemo(() => {
    const extension = union(
      defineBasicExtension(),
      definePlaceholder({ placeholder })
    );

    return createEditor({
      extension,
      defaultContent: value || '<p></p>',
    });
  }, [placeholder]); // 只依赖 placeholder，避免重新创建编辑器

  // 监听文档变化
  useDocChange(() => {
    if (onChange) {
      const html = editor.getDocHTML();
      onChange(html);
    }
  }, { editor });

  return (
    <ProseKit editor={editor}>
      <div className="prosekit-editor-wrapper">
        <Toolbar editor={editor} />
        <div 
          ref={editor.mount} 
          className="prosekit-editor ProseMirror"
        />
      </div>
    </ProseKit>
  );
};

export default ProseKitEditor; 