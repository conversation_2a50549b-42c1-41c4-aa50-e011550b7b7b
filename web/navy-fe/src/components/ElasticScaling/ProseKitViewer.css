.prosekit-viewer {
  background: transparent;
}

.prosekit-content {
  padding: 0;
  font-size: 14px;
  line-height: 1.7;
  color: #2c3e50;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.prosekit-content .ProseMirror {
  outline: none;
  border: none;
  padding: 0;
  margin: 0;
}

.prosekit-content .ProseMirror p {
  margin: 0 0 12px 0;
  padding: 0;
}

.prosekit-content .ProseMirror p:last-child {
  margin-bottom: 0;
}

.prosekit-content .ProseMirror h1,
.prosekit-content .ProseMirror h2,
.prosekit-content .ProseMirror h3,
.prosekit-content .ProseMirror h4,
.prosekit-content .ProseMirror h5 {
  margin: 18px 0 10px 0;
  font-weight: 600;
  color: #1d39c4;
  position: relative;
}

.prosekit-content .ProseMirror h1:first-child,
.prosekit-content .ProseMirror h2:first-child,
.prosekit-content .ProseMirror h3:first-child,
.prosekit-content .ProseMirror h4:first-child,
.prosekit-content .ProseMirror h5:first-child {
  margin-top: 0;
}

.prosekit-content .ProseMirror h1 {
  font-size: 24px;
}

.prosekit-content .ProseMirror h2 {
  font-size: 20px;
}

.prosekit-content .ProseMirror h3 {
  font-size: 16px;
}

.prosekit-content .ProseMirror h4 {
  font-size: 15px;
  padding-left: 12px;
}

.prosekit-content .ProseMirror h4::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background: linear-gradient(to bottom, #1890ff, #40a9ff);
  border-radius: 2px;
}

.prosekit-content .ProseMirror h5 {
  font-size: 14px;
  padding-left: 12px;
  color: #595959;
}

.prosekit-content .ProseMirror h5::before {
  content: '●';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  color: #1890ff;
  font-size: 8px;
}

.prosekit-content .ProseMirror ul,
.prosekit-content .ProseMirror ol {
  padding-left: 20px;
  margin: 12px 0;
}

.prosekit-content .ProseMirror li {
  margin: 6px 0;
  line-height: 1.8;
}

.prosekit-content .ProseMirror ul li::marker {
  color: #1890ff;
}

.prosekit-content .ProseMirror strong {
  color: #1d39c4;
  font-weight: 600;
}

.prosekit-content .ProseMirror em {
  color: #722ed1;
}

.prosekit-content .ProseMirror code {
  background: linear-gradient(135deg, #f0f7ff 0%, #e6f4ff 100%);
  border: 1px solid #b7e7ff;
  border-radius: 4px;
  padding: 3px 6px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 13px;
  color: #1d39c4;
  font-weight: 500;
} 