/* ToMoon 艺术字样式 */
.tomoon-title-wrapper {
  position: relative;
  display: inline-block;
  padding: 6px 12px;
  border-radius: 12px;
  background: linear-gradient(145deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.08));
  border: 1px solid rgba(102, 126, 234, 0.2);
  box-shadow: 
    0 4px 15px rgba(102, 126, 234, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.tomoon-title-wrapper:hover {
  transform: translateY(-1px);
  box-shadow: 
    0 6px 20px rgba(102, 126, 234, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(102, 126, 234, 0.3);
}

.tomoon-title {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
  background-size: 200% 100%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  font-size: 28px !important;
  font-weight: 700 !important;
  margin: 0 !important;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  letter-spacing: 0.8px;
  line-height: 1.2;
  position: relative;
  animation: gradient-shift 4s ease-in-out infinite;
}

/* 渐变动画效果 */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 添加闪烁效果 */
.tomoon-title::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  opacity: 0;
  animation: shine 3s ease-in-out infinite;
}

@keyframes shine {
  0%, 90%, 100% {
    opacity: 0;
  }
  45%, 55% {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tomoon-title {
    font-size: 24px !important;
    letter-spacing: 0.5px;
  }
  
  .tomoon-title-wrapper {
    padding: 4px 8px;
    border-radius: 8px;
  }
}

/* 在深色模式下的适配 */
@media (prefers-color-scheme: dark) {
  .tomoon-title-wrapper {
    background: linear-gradient(145deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
    border-color: rgba(102, 126, 234, 0.3);
    box-shadow: 
      0 4px 15px rgba(102, 126, 234, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  
  .tomoon-title {
    background: linear-gradient(135deg, #8fa5f3 0%, #9d7cc7 50%, #8fa5f3 100%);
    background-size: 200% 100%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .tomoon-title {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
} 