package portal

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// SOPDocument SOP文档模型
type SOPDocument struct {
	BaseModel
	Title       string      `gorm:"column:title;type:varchar(255);not null"`             // 文档标题
	Content     string      `gorm:"column:content;type:longtext"`                        // 文档内容(HTML/Markdown)
	ContentType string      `gorm:"column:content_type;type:varchar(50);default:'html'"` // 内容类型: html, markdown
	Status      string      `gorm:"column:status;type:varchar(50);default:'draft'"`      // 状态: draft, published, archived
	Version     string      `gorm:"column:version;type:varchar(50);default:'1.0'"`       // 版本号
	Category    string      `gorm:"column:category;type:varchar(100)"`                   // 分类
	Tags        string      `gorm:"column:tags;type:text"`                               // 标签(JSON数组)
	Author      string      `gorm:"column:author;type:varchar(100)"`                     // 作者
	Description string      `gorm:"column:description;type:text"`                        // 描述
	Metadata    SOPMetadata `gorm:"column:metadata;type:json"`                           // 元数据
}

// SOPTemplate SOP模板模型
type SOPTemplate struct {
	BaseModel
	Name        string       `gorm:"column:name;type:varchar(255);not null"`               // 模板名称
	Description string       `gorm:"column:description;type:text"`                         // 模板描述
	Content     string       `gorm:"column:content;type:longtext"`                         // 模板内容
	ContentType string       `gorm:"column:content_type;type:varchar(50);default:'html'"`  // 内容类型
	Category    string       `gorm:"column:category;type:varchar(100)"`                    // 分类
	Variables   SOPVariables `gorm:"column:variables;type:json"`                           // 模板变量定义
	SourceURL   string       `gorm:"column:source_url;type:text"`                          // 源URL(如果从网页抓取)
	SourceType  string       `gorm:"column:source_type;type:varchar(50);default:'manual'"` // 来源类型: manual, scraped
	IsActive    bool         `gorm:"column:is_active;type:boolean;default:true"`           // 是否激活
	UsageCount  int          `gorm:"column:usage_count;type:int;default:0"`                // 使用次数
}

// SOPGenerationHistory SOP生成历史记录
type SOPGenerationHistory struct {
	BaseModel
	DocumentID     int           `gorm:"column:document_id;type:bigint"`                   // 关联的文档ID
	Document       SOPDocument   `gorm:"foreignKey:DocumentID"`                            // 关联的文档
	TemplateID     int           `gorm:"column:template_id;type:bigint"`                   // 使用的模板ID
	Template       SOPTemplate   `gorm:"foreignKey:TemplateID"`                            // 使用的模板
	ObjectInfo     SOPObjectInfo `gorm:"column:object_info;type:json"`                     // 操作对象信息
	GenerationType string        `gorm:"column:generation_type;type:varchar(50)"`          // 生成类型: ai_generated, manual
	Status         string        `gorm:"column:status;type:varchar(50);default:'pending'"` // 状态: pending, generating, completed, failed
	Progress       int           `gorm:"column:progress;type:int;default:0"`               // 生成进度(0-100)
	ErrorMessage   string        `gorm:"column:error_message;type:text"`                   // 错误信息
	GeneratedBy    string        `gorm:"column:generated_by;type:varchar(100)"`            // 生成者
	Duration       int           `gorm:"column:duration;type:int"`                         // 生成耗时(毫秒)
	TokensUsed     int           `gorm:"column:tokens_used;type:int"`                      // 使用的Token数量
	AIModel        string        `gorm:"column:ai_model;type:varchar(100)"`                // 使用的AI模型
	Prompt         string        `gorm:"column:prompt;type:longtext"`                      // 使用的提示词
}

// SOPMetadata SOP元数据结构
type SOPMetadata struct {
	Keywords      []string               `json:"keywords,omitempty"`       // 关键词
	Difficulty    string                 `json:"difficulty,omitempty"`     // 难度级别
	EstimatedTime string                 `json:"estimated_time,omitempty"` // 预估时间
	Prerequisites []string               `json:"prerequisites,omitempty"`  // 前置条件
	Tools         []string               `json:"tools,omitempty"`          // 所需工具
	References    []string               `json:"references,omitempty"`     // 参考资料
	CustomFields  map[string]interface{} `json:"custom_fields,omitempty"`  // 自定义字段
}

// SOPVariables 模板变量定义
type SOPVariables struct {
	Variables []SOPVariable `json:"variables,omitempty"` // 变量列表
}

// SOPVariable 单个变量定义
type SOPVariable struct {
	Name         string      `json:"name"`                    // 变量名
	Type         string      `json:"type"`                    // 变量类型: string, number, boolean, date, select
	Label        string      `json:"label"`                   // 显示标签
	Description  string      `json:"description,omitempty"`   // 描述
	Required     bool        `json:"required,omitempty"`      // 是否必填
	DefaultValue interface{} `json:"default_value,omitempty"` // 默认值
	Options      []string    `json:"options,omitempty"`       // 选项(用于select类型)
	Placeholder  string      `json:"placeholder,omitempty"`   // 占位符
	Validation   string      `json:"validation,omitempty"`    // 验证规则
}

// SOPObjectInfo 操作对象信息
type SOPObjectInfo struct {
	TargetName       string                 `json:"target_name"`                 // 目标对象名称
	BatchInfo        string                 `json:"batch_info,omitempty"`        // 批次信息
	CustomParameters map[string]interface{} `json:"custom_parameters,omitempty"` // 自定义参数
	Environment      string                 `json:"environment,omitempty"`       // 环境信息
	Operator         string                 `json:"operator,omitempty"`          // 操作员
	Department       string                 `json:"department,omitempty"`        // 部门
	ContactInfo      string                 `json:"contact_info,omitempty"`      // 联系信息
}

// Value 实现 driver.Valuer 接口，用于数据库存储
func (m SOPMetadata) Value() (driver.Value, error) {
	return json.Marshal(m)
}

// Scan 实现 sql.Scanner 接口，用于数据库读取
func (m *SOPMetadata) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into SOPMetadata", value)
	}

	return json.Unmarshal(bytes, m)
}

// Value 实现 driver.Valuer 接口
func (v SOPVariables) Value() (driver.Value, error) {
	return json.Marshal(v)
}

// Scan 实现 sql.Scanner 接口
func (v *SOPVariables) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into SOPVariables", value)
	}

	return json.Unmarshal(bytes, v)
}

// Value 实现 driver.Valuer 接口
func (o SOPObjectInfo) Value() (driver.Value, error) {
	return json.Marshal(o)
}

// Scan 实现 sql.Scanner 接口
func (o *SOPObjectInfo) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into SOPObjectInfo", value)
	}

	return json.Unmarshal(bytes, o)
}

// TableName 指定表名
func (SOPDocument) TableName() string {
	return "sop_documents"
}

// TableName 指定表名
func (SOPTemplate) TableName() string {
	return "sop_templates"
}

// TableName 指定表名
func (SOPGenerationHistory) TableName() string {
	return "sop_generation_history"
}
